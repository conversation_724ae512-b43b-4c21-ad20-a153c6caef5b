package org.dromara.system.schedule;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.tenant.helper.TenantHelper;
import org.dromara.system.domain.SysUser;
import org.dromara.system.domain.bo.SysUserBo;
import org.dromara.system.domain.vo.SysUserVo;
import org.dromara.system.mapper.SysUserMapper;
import org.dromara.system.service.ISysConfigService;
import org.dromara.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import java.util.*;

@Slf4j
@Component
@EnableScheduling
public class UserSynchronization {

    /** 水务平台账号密码配置 */
    @Value("${waterPlatform.username}")
    private String username;

    @Value("${waterPlatform.password}")
    private String password;

    /** 水务平台地址配置 */
    @Value("${waterPlatform.url}")
    private String url;

    /** 水务平台接口配置 */
    private static final String login = "/api/auth/login";

    private static final String getAllUsers = "/api/getAllUsers";

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Autowired
    private ISysUserService userService;

    @Autowired
    private SysUserMapper userMapper;

    @Autowired
    private ISysConfigService configService;

    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }

    /**
     * 每小时执行一次，获取外部平台的用户信息
     */
    @Scheduled(cron = "0 */5 * * * ?")
    @PostConstruct
    public void syncUsersFromExternalPlatform() {
        log.info("开始执行用户信息同步任务...");
        try {
            // 1. 调用登录接口获取token
            String token = login();
            if (token == null) {
                log.error("登录失败，无法获取token");
                return;
            }

            // 2. 使用token调用获取用户接口
            getUsersWithTokenAndSynchronization(token);

        } catch (Exception e) {
            log.error("同步用户信息时发生错误", e);
        }
    }

    /**
     * 调用登录接口获取token
     *
     * @return token字符串，登录失败返回null
     */
    private String login() {
        try {
            RestTemplate restTemplate = restTemplate();

            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 构建请求体
            Map<String, String> requestBody = new HashMap<>();
            requestBody.put("username", username);
            requestBody.put("password", password);

            HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(requestBody, headers);

            // 发送请求
            ResponseEntity<String> response = restTemplate.exchange(
                url + login,
                HttpMethod.POST,
                requestEntity,
                String.class
            );

            // 处理响应
            if (response.getStatusCode() == HttpStatus.OK) {
                String responseBody = response.getBody();
                if (responseBody != null) {
                    JsonNode jsonNode = objectMapper.readTree(responseBody);
                    if (jsonNode.has("token")) {
                        String token = jsonNode.get("token").asText();
                        log.info("登录成功，获取到token");
                        return token;
                    }else {
                        log.error("登录响应中没有找到token");
                    }
                }
                log.error("登录响应格式不正确: {}", responseBody);
            } else {
                log.error("登录请求失败，状态码: {}", response.getStatusCode());
            }
        } catch (Exception e) {
            log.error("登录过程中发生异常", e);
        }

        return null;
    }

    /**
     * 使用token调用获取用户接口并同步用户信息
     *
     * @param token 登录获取的token
     */
    private void getUsersWithTokenAndSynchronization(String token) {
        try {
            RestTemplate restTemplate = restTemplate();

            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("X-Authorization", "bearer " + token);

            HttpEntity<String> requestEntity = new HttpEntity<>(headers);

            // 发送请求
            ResponseEntity<String> response = restTemplate.exchange(
                url + getAllUsers,
                HttpMethod.GET,
                requestEntity,
                String.class
            );

            // 处理响应
            if (response.getStatusCode() == HttpStatus.OK) {
                String responseBody = response.getBody();
                if (responseBody != null) {
                    JsonNode jsonNode = objectMapper.readTree(responseBody);
                    log.info("获取用户信息成功，开始同步用户数据");

                    // 处理用户数据
                    if (jsonNode.isArray()) {
                        // 获取所有外部平台用户ID，用于后续删除不存在的用户
                        Set<String> externalUserIds = new HashSet<>();

                        // 处理用户数据
                        for (JsonNode userData : jsonNode) {
                            processUserData(userData, externalUserIds);
                        }

                        // 处理需要删除的用户
                        handleDeletedUsers(externalUserIds);
                    } else {
                        log.error("用户数据不是数组格式");
                    }
                }
            } else {
                log.error("获取用户信息请求失败，状态码: {}", response.getStatusCode());
            }
        } catch (Exception e) {
            log.error("获取用户信息过程中发生异常", e);
        }
    }

    /**
     * 处理单个用户数据
     *
     * @param userData 用户数据
     * @param externalUserIds 外部用户ID集合，用于记录所有外部用户ID
     */
    private void processUserData(JsonNode userData, Set<String> externalUserIds) {
        try {
            // 提取用户信息
            String externalPlatformId = userData.has("userId") && !userData.get("userId").isNull() ? userData.get("userId").asText() : null;
            String email = userData.has("email") ? userData.get("email").asText() : null;
            String firstName = userData.has("firstName") ? userData.get("firstName").asText() : null;
//            String name = userData.has("name") ? userData.get("name").asText() : null;
            String phone = userData.has("phone") ? userData.get("phone").asText() : null;
            String password = userData.has("password") ? userData.get("password").asText() : null;

            // 添加到外部用户ID集合
            if (externalPlatformId != null) {
                externalUserIds.add(externalPlatformId);
            }

            if (StringUtils.isBlank(externalPlatformId)) {
                log.error("用户数据处理 - externalPlatformId不能为空");
                return;
            }

//            if (StringUtils.isBlank(firstName)) {
//                log.error("用户数据处理 - externalPlatformId: {}, firstName（账号）不能为空", externalPlatformId);
//                return;
//            }

            log.info("处理用户数据 - externalPlatformId: {}, firstName: {}, email: {}, phone: {}",
                externalPlatformId, firstName, email, phone);

            // 根据externalPlatformId查询用户
            SysUserVo existingUser = userService.selectUserByExternalPlatformIdAndUserType(externalPlatformId, "water_platform_user");

            if (existingUser == null) {
                // 用户不存在，执行新增操作
                if (StringUtils.isBlank(password)) {
                    log.error("用户数据处理 - externalPlatformId: {}, 新增用户时密码不能为空", externalPlatformId);
                    return;
                }

                // 检查用户名是否已存在
                SysUserVo sysUser = userService.selectUserByUserName(firstName);
                if (sysUser != null) {
                    log.error("用户数据处理 - externalPlatformId: {}, 用户名与平台内重复", externalPlatformId);
                    return;
                }

                // 创建新用户
                SysUserBo sysUserBo = new SysUserBo();
                sysUserBo.setExternalPlatformId(externalPlatformId);
                sysUserBo.setUserName(email);
                sysUserBo.setNickName(firstName);
                sysUserBo.setPhonenumber(phone);
                sysUserBo.setEmail(email);
                sysUserBo.setPassword(password);

                // 设置部门ID
                if (StringUtils.isNotBlank(configService.selectConfigByKey("sys.user.synchronization.initDept.id"))) {
                    sysUserBo.setDeptId(Long.valueOf(configService.selectConfigByKey("sys.user.synchronization.initDept.id")));
                }

                // 设置角色ID
                if (StringUtils.isNotBlank(configService.selectConfigByKey("sys.user.synchronization.initRole.id"))) {
                    Long[] roleIds = {Long.valueOf(configService.selectConfigByKey("sys.user.synchronization.initRole.id"))};
                    sysUserBo.setRoleIds(roleIds);
                }

                sysUserBo.setUserType("water_platform_user");

                // 忽略租户配置，执行新增操作
                TenantHelper.ignore(() -> { userService.MQInsertUser(sysUserBo); });
                log.info("用户新增成功 - externalPlatformId: {}", externalPlatformId);
            } else {
                // 用户存在，执行更新操作
                // 检查用户信息是否有变化
                boolean needUpdate = false;

                SysUserBo sysUserBo = new SysUserBo();
                sysUserBo.setUserId(existingUser.getUserId());

                // 检查并更新用户名
                if (StringUtils.isNotBlank(email) && !email.equals(existingUser.getUserName())) {
                    sysUserBo.setUserName(email);
                    needUpdate = true;
                }

                // 检查并更新昵称
                if (StringUtils.isNotBlank(firstName) && !firstName.equals(existingUser.getNickName())) {
                    sysUserBo.setNickName(firstName);
                    needUpdate = true;
                }

                // 检查并更新手机号
                if (StringUtils.isNotBlank(phone) && !phone.equals(existingUser.getPhonenumber())) {
                    sysUserBo.setPhonenumber(phone);
                    needUpdate = true;
                }

                // 检查并更新邮箱
                if (StringUtils.isNotBlank(email) && !email.equals(existingUser.getEmail())) {
                    sysUserBo.setEmail(email);
                    needUpdate = true;
                }

                // 检查并更新密码
                if (StringUtils.isNotBlank(password)) {
                    sysUserBo.setPassword(password);
                    needUpdate = true;
                }

                // 保留原有角色和部门信息
                sysUserBo.setRoleIds(existingUser.getRoleIds());
                sysUserBo.setDeptId(existingUser.getDeptId());

                // 如果有信息变化，执行更新操作
                if (needUpdate) {
                    // 忽略租户配置，执行更新操作
                    TenantHelper.ignore(() -> { userService.MQUpdateUser(sysUserBo); });
                    log.info("用户信息更新成功 - externalPlatformId: {}", externalPlatformId);
                } else {
                    log.info("用户信息无变化，无需更新 - externalPlatformId: {}", externalPlatformId);
                }
            }
        } catch (Exception e) {
            log.error("处理用户数据时发生异常", e);
        }
    }

    /**
     * 处理需要删除的用户
     *
     * @param externalUserIds 当前外部平台的所有用户ID
     */
    private void handleDeletedUsers(Set<String> externalUserIds) {
        try {
            // 构建查询条件，查询所有water_platform_user类型的用户
            QueryWrapper<SysUser> wrapper = Wrappers.query();
            wrapper.eq("user_type", "water_platform_user");
            // 获取所有本地water_platform_user类型的用户
            List<SysUser> localUsers = userMapper.selectList(wrapper);
            if (localUsers != null && !localUsers.isEmpty()) {
                for (SysUser localUser : localUsers) {
                    String externalId = localUser.getExternalPlatformId();

                    // 如果本地用户的externalPlatformId不在外部平台用户ID集合中，说明该用户已被删除
                    if (StringUtils.isNotBlank(externalId) && !externalUserIds.contains(externalId)) {
                        // 执行删除操作
                        TenantHelper.ignore(() -> { userService.deleteUserById(localUser.getUserId()); });
                        log.info("用户已从外部平台删除，本地同步删除成功 - userId: {}, externalPlatformId: {}",
                            localUser.getUserId(), externalId);
                    }
                }
            }
        } catch (Exception e) {
            log.error("处理需要删除的用户时发生异常", e);
        }
    }
}
