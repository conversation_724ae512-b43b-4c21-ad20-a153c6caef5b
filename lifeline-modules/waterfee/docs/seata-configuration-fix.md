# Seata配置问题修复指南

## 问题描述

在启动应用时遇到以下错误：

```
Failed to get available servers: service.vgroupMapping.waterfee-performance-optimized-group configuration item is required
```

以及Spring Boot配置键重命名警告。

## 解决方案

### 1. 配置键重命名修复

已修复以下配置键：

| 旧配置键 | 新配置键 |
|---------|---------|
| `management.metrics.export.prometheus.enabled` | `management.prometheus.metrics.export.enabled` |
| `management.metrics.export.prometheus.step` | `management.prometheus.metrics.export.step` |
| `mybatis-plus.configuration.default-scripting-language` | `mybatis-plus.configuration.default-scripting-language-driver` |

### 2. Seata配置解决方案

提供了三种Seata配置方案：

#### 方案一：启用Seata分布式事务（推荐用于微服务架构）

```yaml
# application.yml
spring:
  profiles:
    include: 
      - performance-complete
      - seata
```

**环境变量配置**：
```bash
# 启用Seata
export SEATA_ENABLED=true
export SEATA_TX_SERVICE_GROUP=waterfee-performance-optimized-group
export SEATA_SERVER_ADDR=127.0.0.1:8091

# 如果使用Nacos作为注册中心
export SEATA_REGISTRY_TYPE=nacos
export SEATA_REGISTRY_NACOS_ADDR=127.0.0.1:8848
export SEATA_REGISTRY_NACOS_GROUP=SEATA_GROUP
```

#### 方案二：禁用Seata（推荐用于单体应用）

```yaml
# application.yml
spring:
  profiles:
    include: 
      - performance-complete
      - no-seata
```

#### 方案三：完全移除Seata依赖

如果项目不需要分布式事务，可以从pom.xml中移除Seata相关依赖：

```xml
<!-- 移除这些依赖 -->
<!--
<dependency>
    <groupId>com.alibaba.cloud</groupId>
    <artifactId>spring-cloud-starter-alibaba-seata</artifactId>
</dependency>
<dependency>
    <groupId>io.seata</groupId>
    <artifactId>seata-spring-boot-starter</artifactId>
</dependency>
-->
```

## 配置文件说明

### application-seata.yml

完整的Seata配置文件，支持：
- 多种注册中心（Nacos、Eureka、Redis等）
- 多种配置中心（Nacos、Apollo、Consul等）
- AT、XA、SAGA事务模式
- 完整的客户端配置

### application-no-seata.yml

禁用Seata的配置文件，适用于：
- 单体应用
- 不需要分布式事务的场景
- 避免Seata配置错误

## 使用建议

### 1. 单体应用（推荐）

```yaml
# application.yml
spring:
  application:
    name: waterfee-performance-optimized
  profiles:
    include: 
      - performance-complete
      - no-seata
```

### 2. 微服务架构

```yaml
# application.yml
spring:
  application:
    name: waterfee-performance-optimized
  profiles:
    include: 
      - performance-complete
      - seata

# 环境变量
SEATA_ENABLED=true
SEATA_SERVER_ADDR=your-seata-server:8091
SEATA_REGISTRY_TYPE=nacos
SEATA_REGISTRY_NACOS_ADDR=your-nacos:8848
```

### 3. 开发环境

```yaml
# application.yml
spring:
  profiles:
    active: dev
    include: 
      - performance-complete
      - no-seata  # 开发环境通常不需要分布式事务
```

### 4. 生产环境

```yaml
# application.yml
spring:
  profiles:
    active: prod
    include: 
      - performance-complete
      - seata  # 生产环境如果是微服务架构则启用
```

## 验证修复

### 1. 检查配置

```bash
# 验证配置文件
validate-config.bat
```

### 2. 启动测试

```bash
# 启动应用
java -jar waterfee-app.jar --spring.profiles.active=prod,performance-complete,no-seata
```

### 3. 检查日志

启动后应该不再出现以下错误：
- ❌ `service.vgroupMapping.waterfee-performance-optimized-group configuration item is required`
- ❌ `The use of configuration keys that have been renamed was found`

## 常见问题

### Q1: 仍然出现Seata配置错误？

**A**: 确保使用了正确的配置文件组合：
```yaml
spring:
  profiles:
    include: 
      - performance-complete
      - no-seata  # 注意这里
```

### Q2: 需要分布式事务但不知道如何配置Seata服务器？

**A**: 可以使用Docker快速启动Seata服务器：
```bash
docker run --name seata-server -p 8091:8091 seataio/seata-server:latest
```

### Q3: 配置键重命名警告仍然存在？

**A**: 检查是否还有其他配置文件使用了旧的配置键，全局搜索并替换。

## 总结

通过以上修复：
1. ✅ 解决了Seata配置缺失问题
2. ✅ 修复了Spring Boot配置键重命名警告
3. ✅ 提供了灵活的配置方案
4. ✅ 支持不同的部署场景

选择适合您项目的配置方案即可正常启动应用。
