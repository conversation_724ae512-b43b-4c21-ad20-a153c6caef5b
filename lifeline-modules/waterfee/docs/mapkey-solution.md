# @MapKey 问题解决方案

## 问题描述

在使用 MyBatis 时，如果方法返回 `Map<String, Map<String, Object>>` 类型，MyBatis 需要知道使用哪个字段作为 Map 的键，这时需要使用 `@MapKey` 注解。

## 解决方案

我们提供了两种解决方案来处理这个问题：

### 方案1：使用 List 返回（推荐）

**优点**：
- 更灵活，可以在 Java 代码中进行任意转换
- 不依赖 MyBatis 的特定注解
- 更容易测试和调试

**实现**：
```java
// Mapper 接口
List<Map<String, Object>> batchCalculateBillStatistics(@Param("customerIds") Collection<Long> customerIds);

// Service 层转换
List<Map<String, Object>> statisticsList = billMapper.batchCalculateBillStatistics(customerIds);
Map<Long, Map<String, Object>> statisticsMap = statisticsList.stream()
        .collect(Collectors.toMap(
                stat -> (Long) stat.get("customer_id"),
                stat -> stat
        ));
```

### 方案2：使用 @MapKey 注解

**优点**：
- 直接返回 Map 结构，减少转换步骤
- MyBatis 原生支持

**实现**：
```java
// Mapper 接口
@MapKey("customer_id")
Map<String, Map<String, Object>> batchCalculateBillStatisticsAsMap(@Param("customerIds") Collection<Long> customerIds);

// Service 层使用
Map<String, Map<String, Object>> rawMap = billMapper.batchCalculateBillStatisticsAsMap(customerIds);
// 如果需要 Long 类型的键，可以进行转换
Map<Long, Map<String, Object>> statisticsMap = rawMap.entrySet().stream()
        .collect(Collectors.toMap(
                entry -> Long.valueOf(entry.getKey()),
                Map.Entry::getValue
        ));
```

## 当前实现

在 `WaterfeeBillMapper` 中，我们同时提供了两种方法：

### 批量获取账单详情

```java
// 方案1：返回 List
List<Map<String, Object>> batchGetBillDetailsWithCustomer(@Param("billIds") Collection<Long> billIds);

// 方案2：使用 @MapKey
@MapKey("bill_id")
Map<String, Map<String, Object>> batchGetBillDetailsWithCustomerAsMap(@Param("billIds") Collection<Long> billIds);
```

### 批量计算统计信息

```java
// 方案1：返回 List
List<Map<String, Object>> batchCalculateBillStatistics(@Param("customerIds") Collection<Long> customerIds);

// 方案2：使用 @MapKey
@MapKey("customer_id")
Map<String, Map<String, Object>> batchCalculateBillStatisticsAsMap(@Param("customerIds") Collection<Long> customerIds);
```

## 使用建议

### 默认使用方案1

在 `BillBatchOptimizationService` 中，默认使用方案1：

```java
public Map<Long, Map<String, Object>> batchCalculateBillStatistics(Collection<Long> customerIds) {
    // 使用 List 返回的方法
    List<Map<String, Object>> statisticsList = billMapper.batchCalculateBillStatistics(customerIds);
    
    // 在 Java 代码中转换
    Map<Long, Map<String, Object>> statisticsMap = statisticsList.stream()
            .collect(Collectors.toMap(
                    stat -> (Long) stat.get("customer_id"),
                    stat -> stat
            ));
    
    return statisticsMap;
}
```

### 如果遇到 @MapKey 错误

如果在运行时遇到 `@MapKey is required` 错误，可以切换到方案2：

```java
public Map<Long, Map<String, Object>> batchCalculateBillStatistics(Collection<Long> customerIds) {
    // 使用 @MapKey 注解的方法
    Map<String, Map<String, Object>> rawMap = billMapper.batchCalculateBillStatisticsAsMap(customerIds);
    
    // 转换键的类型
    Map<Long, Map<String, Object>> statisticsMap = rawMap.entrySet().stream()
            .collect(Collectors.toMap(
                    entry -> Long.valueOf(entry.getKey()),
                    Map.Entry::getValue
            ));
    
    return statisticsMap;
}
```

## 注意事项

1. **键的类型**：`@MapKey` 注解指定的字段值会被转换为 String 类型作为 Map 的键
2. **字段名称**：确保 `@MapKey` 中指定的字段名与 SQL 查询结果中的字段名完全一致
3. **性能考虑**：两种方案的性能差异很小，选择更适合项目需求的方案即可

## 故障排查

如果遇到相关问题：

1. **检查字段名**：确保 `@MapKey` 中的字段名与 SQL 结果一致
2. **检查 SQL**：确保查询结果包含指定的键字段
3. **检查类型转换**：注意 Map 键的类型转换
4. **查看日志**：检查 MyBatis 的详细错误信息

## 总结

- **推荐使用方案1**：更灵活，更容易维护
- **方案2作为备选**：如果确实需要使用 `@MapKey`
- **两种方案都已实现**：可以根据实际情况选择使用
