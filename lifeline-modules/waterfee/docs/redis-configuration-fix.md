# Redis配置冲突问题修复指南

## 问题描述

在启动应用时遇到以下错误：

```
java.lang.IllegalStateException: sentinel servers config already used!
```

这个错误表明Redisson配置中同时配置了哨兵模式和单机模式，导致配置冲突。

## 问题原因

1. **配置冲突**：在配置文件中同时定义了Redis的哨兵配置和单机配置
2. **Redisson初始化错误**：Redisson尝试同时使用多种Redis连接模式
3. **Spring Boot自动配置冲突**：Spring Boot Redis自动配置与Redisson配置冲突

## 解决方案

### 方案一：使用Redis单机模式（推荐）

适用于：开发环境、测试环境、小型生产环境

```yaml
# application.yml
spring:
  application:
    name: waterfee-performance-optimized
  profiles:
    include: 
      - performance-complete
      - no-seata
      - redis-single  # 使用Redis单机模式
```

**环境变量配置**：
```bash
export REDIS_HOST=localhost
export REDIS_PORT=6379
export REDIS_PASSWORD=
export REDIS_DATABASE=0
export REDIS_MAX_ACTIVE=50
```

### 方案二：使用Redis集群模式

适用于：大型生产环境、高并发场景

```yaml
# application.yml
spring:
  profiles:
    include: 
      - performance-complete
      - no-seata
      - redis-cluster  # 使用Redis集群模式
```

**环境变量配置**：
```bash
export REDIS_CLUSTER_NODES=127.0.0.1:7000,127.0.0.1:7001,127.0.0.1:7002,127.0.0.1:7003,127.0.0.1:7004,127.0.0.1:7005
export REDIS_CLUSTER_MAX_REDIRECTS=3
export REDIS_PASSWORD=
export REDIS_MAX_ACTIVE=100
```

### 方案三：使用Redis哨兵模式

适用于：高可用生产环境

```yaml
# application.yml
spring:
  profiles:
    include: 
      - performance-complete
      - no-seata
      - redis-sentinel  # 使用Redis哨兵模式
```

**环境变量配置**：
```bash
export REDIS_SENTINEL_MASTER=mymaster
export REDIS_SENTINEL_NODES=127.0.0.1:26379,127.0.0.1:26380,127.0.0.1:26381
export REDIS_PASSWORD=
export REDIS_DATABASE=0
export REDIS_MAX_ACTIVE=100
```

## 配置文件说明

### application-redis-single.yml

Redis单机模式配置：
- 适合开发和测试环境
- 配置简单，易于维护
- 支持连接池优化
- 包含Redisson单机配置

### application-redis-cluster.yml

Redis集群模式配置：
- 适合大型生产环境
- 支持水平扩展
- 高可用性
- 自动故障转移

### application-redis-sentinel.yml

Redis哨兵模式配置：
- 适合高可用场景
- 主从自动切换
- 监控和通知功能
- 配置相对复杂

## 快速修复

### 1. 使用修复脚本（推荐）

```bash
# 运行Redis配置修复脚本
fix-redis-config.bat
```

脚本会：
- 检查Redis配置文件
- 让您选择合适的Redis模式
- 自动生成正确的配置
- 创建启动脚本

### 2. 手动修复

#### 步骤1：选择Redis模式

根据您的环境选择合适的Redis模式：

- **开发环境**：选择单机模式
- **小型生产环境**：选择单机模式
- **大型生产环境**：选择集群模式
- **高可用生产环境**：选择哨兵模式

#### 步骤2：更新配置文件

```yaml
# application.yml
spring:
  application:
    name: waterfee-performance-optimized
  profiles:
    include: 
      - performance-complete
      - no-seata
      - redis-single  # 根据选择的模式修改
```

#### 步骤3：设置环境变量

根据选择的模式设置相应的环境变量。

#### 步骤4：启动应用

```bash
java -jar waterfee-app.jar
```

## 验证修复

### 1. 检查应用启动

启动应用后，应该不再出现以下错误：
- ❌ `sentinel servers config already used!`
- ❌ `Error creating bean with name 'redisson'`

### 2. 检查Redis连接

```bash
# 检查应用健康状态
curl http://localhost:8080/actuator/health

# 检查Redis连接状态
curl http://localhost:8080/actuator/health/redis
```

### 3. 测试缓存功能

```bash
# 测试性能监控接口（会使用缓存）
curl http://localhost:8080/waterfee/performance/overview
```

## 常见问题

### Q1: 仍然出现Redis配置错误？

**A**: 检查是否有多个Redis配置文件被同时引入：
```yaml
spring:
  profiles:
    include: 
      - redis-single  # 只保留一个Redis配置
      # - redis-cluster  # 注释掉其他配置
      # - redis-sentinel
```

### Q2: Redis连接超时？

**A**: 检查Redis服务是否正在运行，并验证连接参数：
```bash
# 测试Redis连接
redis-cli ping

# 检查Redis服务状态
redis-server --version
```

### Q3: Redisson配置不生效？

**A**: 确保环境变量设置正确，并重启应用：
```bash
# 检查环境变量
echo $REDIS_HOST
echo $REDIS_PORT

# 重启应用
java -jar waterfee-app.jar
```

### Q4: 需要切换Redis模式？

**A**: 重新运行修复脚本选择新的模式：
```bash
fix-redis-config.bat
```

## 性能优化建议

### 单机模式优化

```yaml
# 适合小型应用
redis:
  lettuce:
    pool:
      max-active: 50
      max-idle: 20
      min-idle: 5
```

### 集群模式优化

```yaml
# 适合大型应用
redis:
  lettuce:
    pool:
      max-active: 100
      max-idle: 50
      min-idle: 10
```

### 哨兵模式优化

```yaml
# 适合高可用场景
redis:
  lettuce:
    pool:
      max-active: 100
      max-idle: 50
      min-idle: 10
```

## 总结

通过以上修复：
1. ✅ 解决了Redis配置冲突问题
2. ✅ 提供了三种Redis部署模式选择
3. ✅ 支持不同环境的配置需求
4. ✅ 保持了性能优化功能完整性

选择适合您环境的Redis配置模式，即可正常启动应用并享受性能优化带来的提升。
