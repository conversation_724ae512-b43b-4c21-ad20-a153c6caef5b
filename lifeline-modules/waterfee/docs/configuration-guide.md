# 水费账单性能优化配置指南

## 配置文件概览

本项目提供了多个配置文件来支持不同的性能优化需求和环境：

### 📁 配置文件结构

```
src/main/resources/
├── application-performance-complete.yml    # 完整性能优化配置（推荐）
├── application-performance.yml             # 基础性能优化配置（阶段一）
├── application-advanced-performance.yml    # 高级性能优化配置（阶段二&三）
├── application-performance-dev.yml         # 开发环境配置
├── application-performance-prod.yml        # 生产环境配置
└── sql/
    └── performance-optimization-indexes.sql # 数据库索引优化脚本
```

## 🚀 快速开始

### 1. 选择配置文件

根据您的需求选择合适的配置文件：

#### **推荐方案：使用完整配置**
```yaml
# application.yml
spring:
  profiles:
    include: performance-complete
```

#### **分阶段实施方案**
```yaml
# application.yml
spring:
  profiles:
    include: 
      - performance              # 基础优化
      - advanced-performance     # 高级优化
```

#### **环境特定配置**
```yaml
# application.yml
spring:
  profiles:
    active: dev
    include: 
      - performance-complete
      - performance-dev          # 开发环境特定配置
```

### 2. 环境变量配置

所有配置都支持环境变量覆盖，方便不同环境部署：

```bash
# 数据库配置
export DB_INITIAL_SIZE=20
export DB_MAX_ACTIVE=200
export SLOW_SQL_MILLIS=2000

# Redis配置
export REDIS_MAX_ACTIVE=100
export REDIS_TIMEOUT=5000ms

# 线程池配置
export THREAD_POOL_CORE_SIZE=16
export THREAD_POOL_MAX_SIZE=64

# 监控配置
export PERFORMANCE_MONITORING_ENABLED=true
export MONITORING_COLLECT_INTERVAL=60
```

## 📋 配置详解

### 1. 数据源配置

```yaml
spring:
  datasource:
    druid:
      # 连接池大小配置
      initial-size: ${DB_INITIAL_SIZE:15}      # 初始连接数
      min-idle: ${DB_MIN_IDLE:15}              # 最小空闲连接
      max-active: ${DB_MAX_ACTIVE:150}         # 最大活跃连接
      max-wait: ${DB_MAX_WAIT:60000}           # 最大等待时间(ms)
      
      # 连接有效性检测
      test-while-idle: true                    # 空闲时检测
      validation-query: SELECT 1              # 检测查询
      validation-query-timeout: 3             # 检测超时(s)
      
      # 连接回收配置
      time-between-eviction-runs-millis: 60000    # 回收检测间隔
      min-evictable-idle-time-millis: 300000      # 最小空闲时间
      
      # 监控配置
      filters: stat,wall,slf4j                # 监控过滤器
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=2000
```

### 2. Redis配置

```yaml
spring:
  data:
    redis:
      # 连接池配置
      lettuce:
        pool:
          max-active: ${REDIS_MAX_ACTIVE:50}   # 最大连接数
          max-idle: ${REDIS_MAX_IDLE:20}       # 最大空闲连接
          min-idle: ${REDIS_MIN_IDLE:5}        # 最小空闲连接
          max-wait: ${REDIS_MAX_WAIT:3000ms}   # 最大等待时间
      
      # 连接超时配置
      connect-timeout: ${REDIS_CONNECT_TIMEOUT:3000ms}
      timeout: ${REDIS_TIMEOUT:3000ms}
```

### 3. 线程池配置

```yaml
thread-pool:
  core-pool-size: ${THREAD_POOL_CORE_SIZE:10}        # 核心线程数
  max-pool-size: ${THREAD_POOL_MAX_SIZE:50}          # 最大线程数
  queue-capacity: ${THREAD_POOL_QUEUE_CAPACITY:200}  # 队列容量
  keep-alive-seconds: 60                             # 线程空闲时间
  thread-name-prefix: "waterfee-parallel-"           # 线程名前缀
  rejection-policy: "CallerRunsPolicy"               # 拒绝策略
```

### 4. 缓存配置

```yaml
# 基础缓存配置
cache:
  redis:
    ttl:
      price-config: 24h          # 价格配置缓存24小时
      user-info: 2h              # 用户信息缓存2小时
      billing-calc: 30m          # 计费计算缓存30分钟
      batch-query: 10m           # 批量查询缓存10分钟

# 高级缓存配置
advanced-cache:
  local:
    max-size: ${LOCAL_CACHE_MAX_SIZE:10000}    # 本地缓存最大条目数
    default-ttl-minutes: 60                   # 默认过期时间
  redis:
    key-prefix: "waterfee:advanced:"          # 缓存键前缀
    default-ttl-hours: 1                      # 默认过期时间
```

### 5. 性能监控配置

```yaml
performance:
  monitoring:
    enabled: ${PERFORMANCE_MONITORING_ENABLED:true}
    collect-interval: ${MONITORING_COLLECT_INTERVAL:60}    # 收集间隔(s)
    report-interval: ${MONITORING_REPORT_INTERVAL:300}     # 报告间隔(s)
    
    # 告警阈值配置
    alerts:
      memory-usage-threshold: 80              # 内存使用率告警阈值
      cpu-usage-threshold: 80                 # CPU使用率告警阈值
      cache-hit-rate-threshold: 60            # 缓存命中率告警阈值
      response-time-threshold: 1000           # 响应时间告警阈值(ms)
```

## 🔧 环境特定配置

### 开发环境 (application-performance-dev.yml)

**特点**：
- 较小的连接池配置
- 详细的调试日志
- 较短的缓存时间
- 启用所有监控端点

**适用场景**：
- 本地开发
- 功能测试
- 性能调试

### 生产环境 (application-performance-prod.yml)

**特点**：
- 大容量连接池配置
- 精简的日志输出
- 较长的缓存时间
- 安全的监控配置

**适用场景**：
- 生产部署
- 性能要求高的环境
- 大并发场景

## 📊 性能调优建议

### 1. 数据库连接池调优

```yaml
# 小型系统（< 1000用户）
initial-size: 5
max-active: 20

# 中型系统（1000-10000用户）
initial-size: 15
max-active: 100

# 大型系统（> 10000用户）
initial-size: 30
max-active: 200
```

### 2. 线程池调优

```yaml
# CPU密集型任务
core-pool-size: CPU核心数
max-pool-size: CPU核心数 + 1

# IO密集型任务
core-pool-size: CPU核心数 * 2
max-pool-size: CPU核心数 * 4
```

### 3. 缓存调优

```yaml
# 高频访问数据
ttl: 2-4小时

# 中频访问数据
ttl: 30分钟-1小时

# 低频访问数据
ttl: 5-15分钟
```

## ⚠️ 注意事项

### 1. 配置冲突

- 避免同时引入多个性能配置文件
- 环境特定配置应该放在最后
- 使用环境变量覆盖敏感配置

### 2. 资源限制

- 确保系统有足够的内存支持配置的连接池大小
- 监控线程池队列积压情况
- 定期清理缓存避免内存泄漏

### 3. 安全考虑

- 生产环境关闭不必要的监控端点
- 使用强密码保护监控界面
- 限制日志中的敏感信息输出

## 🔍 故障排查

### 1. 配置验证

```bash
# 检查配置是否生效
curl http://localhost:8080/actuator/configprops

# 检查环境变量
curl http://localhost:8080/actuator/env
```

### 2. 性能监控

```bash
# 检查性能指标
curl http://localhost:8080/waterfee/performance/overview

# 检查健康状态
curl http://localhost:8080/actuator/health
```

### 3. 日志分析

```bash
# 查看性能相关日志
tail -f logs/waterfee-performance.log | grep -E "(WARN|ERROR)"

# 查看慢查询日志
tail -f logs/waterfee-performance.log | grep "slow"
```

## 📚 相关文档

- [性能优化指南](performance-optimization-guide.md)
- [高级性能优化指南](advanced-performance-optimization-guide.md)
- [MapKey问题解决方案](mapkey-solution.md)

## 🆘 技术支持

如有配置问题，请：
1. 检查配置文件语法
2. 验证环境变量设置
3. 查看应用启动日志
4. 联系开发团队
