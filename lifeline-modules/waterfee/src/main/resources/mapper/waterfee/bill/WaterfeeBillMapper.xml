<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.bill.mapper.WaterfeeBillMapper">

    <resultMap type="org.dromara.waterfee.bill.domain.WaterfeeBill" id="WaterfeeBillResult">
        <id property="billId" column="bill_id"/>
        <result property="billNumber" column="bill_number"/>
        <result property="customerId" column="customer_id"/>
        <result property="meterId" column="meter_id"/>
        <result property="meterBookId" column="meter_book_id"/>
        <result property="pricePlanId" column="price_plan_id"/>
        <result property="billingPeriodStart" column="billing_period_start"/>
        <result property="billingPeriodEnd" column="billing_period_end"/>
        <result property="billingIssueDate" column="billing_issue_date"/>
        <result property="billingDueDate" column="billing_due_date"/>
        <result property="previousReadingId" column="previous_reading_id"/>
        <result property="currentReadingId" column="current_reading_id"/>
        <result property="previousReadingValue" column="previous_reading_value"/>
        <result property="currentReadingValue" column="current_reading_value"/>
        <result property="consumptionVolume" column="consumption_volume"/>
        <result property="consumptionUnit" column="consumption_unit"/>
        <result property="baseChargeAmount" column="base_charge_amount"/>
        <result property="tier1" column="tier1"/>
        <result property="tier1Amount" column="tier1_amount"/>
        <result property="tier2" column="tier2"/>
        <result property="tier2Amount" column="tier2_amount"/>
        <result property="tier3" column="tier3"/>
        <result property="tier3Amount" column="tier3_amount"/>
        <result property="surchargeAmount" column="surcharge_amount"/>
        <result property="additionalChargeAmount" column="additional_charge_amount"/>
        <result property="taxAmount" column="tax_amount"/>
        <result property="adjustmentsAmount" column="adjustments_amount"/>
        <result property="totalAmount" column="total_amount"/>
        <result property="amountPaid" column="amount_paid"/>
        <result property="balanceDue" column="balance_due"/>
        <result property="billStatus" column="bill_status"/>
        <result property="billMonth" column="bill_month"/>
        <result property="notes" column="notes"/>
        <result property="remark" column="remark"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="createDept" column="create_dept"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <resultMap id="WaterfeeBillVoResult" type="org.dromara.waterfee.bill.domain.vo.WaterfeeBillVo">
        <id property="billId" column="bill_id"/>
        <result property="billNumber" column="bill_number"/>
        <result property="customerId" column="customer_id"/>
        <result property="userNo" column="user_no"/>
        <result property="userName" column="user_name"/>
        <result property="meterId" column="meter_id"/>
        <result property="meterNo" column="meter_no"/>
        <result property="meterBookId" column="meter_book_id"/>
        <result property="bookName" column="book_name"/>
        <result property="pricePlanId" column="price_plan_id"/>
        <result property="pricePlanName" column="name"/>
        <result property="billingPeriodStart" column="billing_period_start"/>
        <result property="billingPeriodEnd" column="billing_period_end"/>
        <result property="billingIssueDate" column="billing_issue_date"/>
        <result property="billingDueDate" column="billing_due_date"/>
        <result property="previousReadingId" column="previous_reading_id"/>
        <result property="currentReadingId" column="current_reading_id"/>
        <result property="previousReadingValue" column="previous_reading_value"/>
        <result property="currentReadingValue" column="current_reading_value"/>
        <result property="consumptionVolume" column="consumption_volume"/>
        <result property="consumptionUnit" column="consumption_unit"/>
        <result property="baseChargeAmount" column="base_charge_amount"/>
        <result property="adjustmentsAmount" column="adjustments_amount"/>
        <result property="totalAmount" column="total_amount"/>
        <result property="amountPaid" column="amount_paid"/>
        <result property="balanceDue" column="balance_due"/>
        <result property="billStatus" column="bill_status"/>
        <result property="billMonth" column="bill_month"/>
        <result property="notes" column="notes"/>
        <result property="remark" column="remark"/>
        <result property="useWaterNature" column="use_water_nature"/>
        <result property="address" column="address"/>
        <result property="customerNature" column="customer_nature"/>
        <result property="useWaterNumber" column="use_water_number"/>
        <result property="phoneNumber" column="phone_number"/>
        <result property="certificateType" column="certificate_type"/>
        <result property="certificateNumber" column="certificate_number"/>
        <result property="userStatus" column="user_status"/>
        <result property="email" column="email"/>
        <result property="supplyDate" column="supply_date"/>
        <result property="unitRoomNumber" column="unit_room_number"/>
        <result property="communityId" column="community_id"/>
        <result property="communityName" column="community_name"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="selectWaterfeeBillVo">
        select b.bill_id, b.bill_number, b.customer_id, b.meter_id, b.meter_book_id, b.price_plan_id,
        b.billing_period_start, b.billing_period_end, b.billing_issue_date, b.billing_due_date,
        b.previous_reading_id, b.current_reading_id, b.previous_reading_value, b.current_reading_value,
        b.consumption_volume, b.consumption_unit, b.base_charge_amount, b.adjustments_amount, b.total_amount,
        b.amount_paid, b.balance_due,
        b.bill_status, b.bill_month, b.notes, b.remark, b.create_time,
        u.user_no, u.user_name, u.use_water_nature, u.address, u.customer_nature, u.use_water_number,
        u.phone_number, u.certificate_type, u.certificate_number, u.user_status, u.email, u.supply_date,
        u.unit_room_number, u.community_id, wc.community_name,
        m.meter_no, mb.book_name, pc.name
        from waterfee_bills b
        left join waterfee_user u on b.customer_id = u.user_id
        left join waterfee_meter m on b.meter_id = m.meter_id
        left join waterfee_meter_book mb on b.meter_book_id = mb.id
        left join v_waterfee_price_combined pc on b.price_plan_id = pc.id
        left join waterfee_community wc on u.community_id = wc.id
    </sql>

    <select id="selectVoList" resultMap="WaterfeeBillVoResult">
        <include refid="selectWaterfeeBillVo"/>
        ${ew.getCustomSqlSegment()}
    </select>

    <select id="selectVoListByUserId" resultMap="WaterfeeBillVoResult">
        <include refid="selectWaterfeeBillVo"/>
        where u.user_id = #{userId}
        <if test="status != null and status != ''">
            and b.bill_status = #{status}
        </if>
        <if test="month != null and month != ''">
            and b.bill_month = #{month}
        </if>
    </select>

    <select id="selectVoById" resultMap="WaterfeeBillVoResult">
        <include refid="selectWaterfeeBillVo"/>
        where b.bill_id = #{billId}
    </select>

    <select id="selectBillVoPage" resultMap="WaterfeeBillVoResult">
        <include refid="selectWaterfeeBillVo"/>
        ${ew.getCustomSqlSegment().replace("meter_book_id", "b.meter_book_id")}
    </select>

    <select id="selectBillVoPageDetail" resultMap="WaterfeeBillVoResult">
        <include refid="selectWaterfeeBillVo"/>
        ${ew.getCustomSqlSegment().replace("meter_book_id", "b.meter_book_id")}
    </select>

    <!-- 表册账单汇总信息结果映射 -->
    <resultMap id="MeterBookBillSummaryVoResult" type="org.dromara.waterfee.bill.domain.vo.MeterBookBillSummaryVo">
        <id property="meterBookId" column="meter_book_id"/>
        <result property="meterBookNo" column="book_no"/>
        <result property="meterBookName" column="book_name"/>
        <result property="meterReader" column="reader"/>
        <result property="meterReaderName" column="reader_name"/>
        <result property="billMonth" column="bill_month"/>
        <result property="totalBills" column="total_bills"/>
        <result property="totalUsers" column="total_users"/>
        <result property="draftBills" column="draft_bills"/>
        <result property="issuedBills" column="issued_bills"/>
        <result property="paidBills" column="paid_bills"/>
        <result property="overdueBills" column="overdue_bills"/>
        <result property="totalAmount" column="total_amount"/>
        <result property="paidAmount" column="paid_amount"/>
        <result property="unpaidAmount" column="unpaid_amount"/>
        <result property="executeStatus" column="execute_status"/>
        <result property="executor" column="executor"/>
        <result property="executeTime" column="execute_time"/>
    </resultMap>

    <!-- 按表册分组查询账单汇总信息 -->
    <select id="selectMeterBookBillSummaryPage" resultMap="MeterBookBillSummaryVoResult">
        SELECT
        mb.id AS meter_book_id,
        mb.book_no,
        mb.book_name,
        mb.reader,
        mb.reader_name,
        MAX(b.bill_month) AS bill_month,
        COUNT(b.bill_id) AS total_bills,
        COUNT(DISTINCT b.customer_id) AS total_users,
        SUM(CASE WHEN b.bill_status = 'DRAFT' THEN 1 ELSE 0 END) AS draft_bills,
        SUM(CASE WHEN b.bill_status = 'ISSUED' THEN 1 ELSE 0 END) AS issued_bills,
        SUM(CASE WHEN b.bill_status = 'PAID' THEN 1 ELSE 0 END) AS paid_bills,
        SUM(CASE WHEN b.bill_status = 'OVERDUE' THEN 1 ELSE 0 END) AS overdue_bills,
        SUM(b.total_amount) AS total_amount,
        SUM(b.amount_paid) AS paid_amount,
        SUM(b.balance_due) AS unpaid_amount,
        CASE
        WHEN SUM(CASE WHEN b.bill_status = 'DRAFT' THEN 1 ELSE 0 END) > 0 THEN '未发行'
        ELSE '已发行'
        END AS execute_status,
        MAX(su.nick_name) AS executor,
        MAX(b.update_time) AS execute_time
        FROM
        waterfee_meter_book mb
        LEFT JOIN
        waterfee_bills b ON mb.id = b.meter_book_id
        LEFT JOIN
        sys_user su ON b.update_by = su.user_id
        ${ew.getCustomSqlSegment().replaceAll("ORDER BY.*", "").replace("WHERE", "WHERE b.del_flag = '0' AND")}
        GROUP BY
        mb.id, mb.book_no, mb.book_name, mb.reader, mb.reader_name
        ORDER BY
        mb.id
    </select>

    <select id="getMechanicalWatchArrearsDetails" resultType="org.dromara.waterfee.statisticalReport.domain.MechanicalWatchArrearsDetailsVO">
        select
            u.user_no,
            u.user_name,
            u.address,
            u.phone_number,
            u.certificate_number,
            d1.dict_label as customer_nature,
            d2.dict_label as use_water_nature,
            b.billing_period_start,
            b.billing_period_end,
            b.previous_reading_value,
            b.current_reading_value,
            b.consumption_volume,
            b.water_bill_only,
            b.water_resource_tax,
            b.sewage_treatment_fee,
            b.total_amount
        from waterfee_bills b
            left join waterfee_meter m on b.meter_id = m.meter_id
            left join waterfee_user u on m.user_id = u.user_id
            left join waterfee_meter_book book on book.id = b.meter_book_id
            left join sys_dict_data d1 on d1.dict_type = 'waterfee_user_customer_nature' and d1.dict_value = u.customer_nature
            left join sys_dict_data d2 on d2.dict_type = 'waterfee_user_use_water_nature' and d2.dict_value = u.use_water_nature
        where
            b.bill_status = 'PAID'
            and b.del_flag = 0
            and m.meter_type = 1
            and u.user_id is not null
            <if test="meterBookId != null">
                and book.id = #{meterBookId}
            </if>
        order by b.update_time desc
    </select>

    <!-- =====================================================
         批量操作SQL（性能优化）
         ===================================================== -->

    <!-- 批量更新账单状态 -->
    <update id="batchUpdateBillStatus">
        UPDATE waterfee_bills
        SET bill_status = #{status},
            update_by = #{updateBy},
            update_time = #{updateTime}
        WHERE bill_id IN
        <foreach collection="billIds" item="billId" open="(" separator="," close=")">
            #{billId}
        </foreach>
        AND del_flag = '0'
    </update>

    <!-- 批量查询账单信息 -->
    <select id="batchSelectBillsByIds" resultType="org.dromara.waterfee.bill.domain.WaterfeeBill">
        SELECT
            bill_id, bill_number, customer_id, meter_id, meter_book_id,
            billing_period_start, billing_period_end, previous_reading_value, current_reading_value,
            consumption_volume, base_charge_amount, adjustments_amount,
            total_amount, bill_status, billing_due_date, create_time, update_time,
            remark, del_flag, tenant_id
        FROM waterfee_bills
        WHERE bill_id IN
        <foreach collection="billIds" item="billId" open="(" separator="," close=")">
            #{billId}
        </foreach>
        AND del_flag = '0'
        ORDER BY bill_id
    </select>

    <!-- 批量获取账单详细信息（包含客户信息） -->
    <select id="batchGetBillDetailsWithCustomer" resultType="java.util.Map">
        SELECT
            b.bill_id,
            b.bill_number,
            b.customer_id,
            b.meter_id,
            b.total_amount,
            b.bill_status,
            b.billing_due_date,
            u.user_name as customer_name,
            u.phone_number as contact_phone,
            u.address as customer_address,
            u.balance as account_balance,
            0 as frozen_amount,
            u.balance as available_balance
        FROM waterfee_bills b
        LEFT JOIN waterfee_user u ON b.customer_id = u.user_id AND u.del_flag = '0'
        WHERE b.bill_id IN
        <foreach collection="billIds" item="billId" open="(" separator="," close=")">
            #{billId}
        </foreach>
        AND b.del_flag = '0'
        ORDER BY b.bill_id
    </select>

    <!-- 批量获取账单详细信息（包含客户信息）- 返回Map结构 -->
    <select id="batchGetBillDetailsWithCustomerAsMap" resultType="java.util.Map">
        SELECT
            b.bill_id,
            b.bill_number,
            b.customer_id,
            b.meter_id,
            b.total_amount,
            b.bill_status,
            b.billing_due_date,
            u.user_name as customer_name,
            u.phone_number as contact_phone,
            u.address as customer_address,
            u.balance as account_balance,
            0 as frozen_amount,
            u.balance as available_balance
        FROM waterfee_bills b
        LEFT JOIN waterfee_user u ON b.customer_id = u.user_id AND u.del_flag = '0'
        WHERE b.bill_id IN
        <foreach collection="billIds" item="billId" open="(" separator="," close=")">
            #{billId}
        </foreach>
        AND b.del_flag = '0'
        ORDER BY b.bill_id
    </select>

    <!-- 批量发行账单（按表册ID） -->
    <update id="batchIssueBillsByMeterBookIds">
        UPDATE waterfee_bills
        SET bill_status = 'ISSUED',
            billing_issue_date = #{issueDate},
            update_time = #{issueDate}
        WHERE meter_book_id IN
        <foreach collection="meterBookIds" item="meterBookId" open="(" separator="," close=")">
            #{meterBookId}
        </foreach>
        AND bill_status = 'DRAFT'
        AND del_flag = '0'
    </update>

    <!-- 批量计算账单统计信息 -->
    <select id="batchCalculateBillStatistics" resultType="java.util.Map">
        SELECT
            b.customer_id,
            COUNT(*) as total_bills,
            COUNT(CASE WHEN b.bill_status = 'PAID' THEN 1 END) as paid_bills,
            COUNT(CASE WHEN b.bill_status = 'DRAFT' THEN 1 END) as draft_bills,
            COUNT(CASE WHEN b.bill_status = 'ISSUED' THEN 1 END) as issued_bills,
            COUNT(CASE WHEN b.bill_status = 'OVERDUE' THEN 1 END) as overdue_bills,
            COALESCE(SUM(b.total_amount), 0) as total_amount,
            COALESCE(SUM(b.amount_paid), 0) as total_paid,
            COALESCE(SUM(b.balance_due), 0) as total_balance_due,
            COALESCE(SUM(b.consumption_volume), 0) as total_consumption,
            COALESCE(AVG(b.total_amount), 0) as avg_bill_amount,
            MAX(b.billing_due_date) as latest_due_date,
            MIN(b.billing_period_start) as earliest_period_start,
            MAX(b.billing_period_end) as latest_period_end
        FROM waterfee_bills b
        WHERE b.customer_id IN
        <foreach collection="customerIds" item="customerId" open="(" separator="," close=")">
            #{customerId}
        </foreach>
        AND b.del_flag = '0'
        GROUP BY b.customer_id
        ORDER BY b.customer_id
    </select>

    <!-- 批量计算账单统计信息 - 返回Map结构 -->
    <select id="batchCalculateBillStatisticsAsMap" resultType="java.util.Map">
        SELECT
            b.customer_id,
            COUNT(*) as total_bills,
            COUNT(CASE WHEN b.bill_status = 'PAID' THEN 1 END) as paid_bills,
            COUNT(CASE WHEN b.bill_status = 'DRAFT' THEN 1 END) as draft_bills,
            COUNT(CASE WHEN b.bill_status = 'ISSUED' THEN 1 END) as issued_bills,
            COUNT(CASE WHEN b.bill_status = 'OVERDUE' THEN 1 END) as overdue_bills,
            COALESCE(SUM(b.total_amount), 0) as total_amount,
            COALESCE(SUM(b.amount_paid), 0) as total_paid,
            COALESCE(SUM(b.balance_due), 0) as total_balance_due,
            COALESCE(SUM(b.consumption_volume), 0) as total_consumption,
            COALESCE(AVG(b.total_amount), 0) as avg_bill_amount,
            MAX(b.billing_due_date) as latest_due_date,
            MIN(b.billing_period_start) as earliest_period_start,
            MAX(b.billing_period_end) as latest_period_end
        FROM waterfee_bills b
        WHERE b.customer_id IN
        <foreach collection="customerIds" item="customerId" open="(" separator="," close=")">
            #{customerId}
        </foreach>
        AND b.del_flag = '0'
        GROUP BY b.customer_id
        ORDER BY b.customer_id
    </select>

    <!-- 统计无效账单数量（用于删除前校验） -->
    <select id="countInvalidBillsForDeletion" resultType="int">
        SELECT COUNT(*)
        FROM waterfee_bills
        WHERE bill_id IN
        <foreach collection="billIds" item="billId" open="(" separator="," close=")">
            #{billId}
        </foreach>
        AND bill_status NOT IN ('DRAFT', 'CANCELLED')
        AND del_flag = '0'
    </select>

    <!-- 批量逻辑删除账单 -->
    <update id="batchLogicalDeleteBills">
        UPDATE waterfee_bills
        SET del_flag = '1',
            update_time = #{deleteTime}
        WHERE bill_id IN
        <foreach collection="billIds" item="billId" open="(" separator="," close=")">
            #{billId}
        </foreach>
        AND del_flag = '0'
    </update>

</mapper>
