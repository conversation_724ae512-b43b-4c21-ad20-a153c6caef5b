<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.meterReading.mapper.BillProcessingBatchMapper">

    <!-- 批量更新账单状态（优化版本） -->
    <update id="batchUpdateBillStatus">
        UPDATE waterfee_bills
        SET bill_status = #{status},
            update_by = #{updateBy},
            update_time = #{updateTime}
        WHERE bill_id IN
        <foreach collection="billIds" item="billId" open="(" separator="," close=")">
            #{billId}
        </foreach>
        AND del_flag = '0'
    </update>

    <!-- 批量发行账单（按表册ID） -->
    <update id="batchIssueBillsByMeterBookIds">
        UPDATE waterfee_bills
        SET bill_status = 'ISSUED',
            billing_issue_date = #{issueDate},
            update_time = #{issueDate}
        WHERE meter_book_id IN
        <foreach collection="meterBookIds" item="meterBookId" open="(" separator="," close=")">
            #{meterBookId}
        </foreach>
        AND bill_status = 'DRAFT'
        AND del_flag = '0'
    </update>

    <!-- 批量逻辑删除账单 -->
    <update id="batchLogicalDeleteBills">
        UPDATE waterfee_bills
        SET del_flag = '1',
            update_time = #{deleteTime}
        WHERE bill_id IN
        <foreach collection="billIds" item="billId" open="(" separator="," close=")">
            #{billId}
        </foreach>
        AND del_flag = '0'
    </update>

    <!-- 统计无效账单数量（用于删除前校验） -->
    <select id="countInvalidBillsForDeletion" resultType="int">
        SELECT COUNT(*)
        FROM waterfee_bills
        WHERE bill_id IN
        <foreach collection="billIds" item="billId" open="(" separator="," close=")">
            #{billId}
        </foreach>
        AND bill_status NOT IN ('DRAFT', 'CANCELLED')
        AND del_flag = '0'
    </select>

    <!-- 批量插入支付记录 -->
    <insert id="batchInsertPaymentRecords" parameterType="java.util.List">
        INSERT INTO waterfee_payment_detail (
            bill_id, user_id, payment_amount, payment_method, payment_time,
            payment_status, transaction_id, remark, create_by, create_time,
            update_by, update_time, del_flag, tenant_id
        ) VALUES
        <foreach collection="paymentRecords" item="record" separator=",">
            (
                #{record.billId},
                #{record.userId},
                #{record.paymentAmount},
                #{record.paymentMethod},
                #{record.paymentTime},
                #{record.paymentStatus},
                #{record.transactionId},
                #{record.remark},
                #{record.createBy},
                #{record.createTime},
                #{record.updateBy},
                #{record.updateTime},
                #{record.delFlag},
                #{record.tenantId}
            )
        </foreach>
    </insert>

    <!-- 批量更新客户账户余额 -->
    <update id="batchUpdateCustomerBalances">
        <foreach collection="balanceUpdates" item="update" separator=";">
            UPDATE waterfee_user
            SET balance = balance + #{update.balanceChange},
                update_time = NOW()
            WHERE user_id = #{update.customerId}
            AND del_flag = '0'
        </foreach>
    </update>

    <!-- 账单信息结果映射 -->
    <resultMap id="WaterfeeBillVoResult" type="org.dromara.waterfee.bill.domain.vo.WaterfeeBillVo">
        <id property="billId" column="bill_id"/>
        <result property="billNumber" column="bill_number"/>
        <result property="customerId" column="customer_id"/>
        <result property="meterId" column="meter_id"/>
        <result property="meterBookId" column="meter_book_id"/>
        <result property="billingPeriodStart" column="billing_period_start"/>
        <result property="billingPeriodEnd" column="billing_period_end"/>
        <result property="previousReadingValue" column="previous_reading_value"/>
        <result property="currentReadingValue" column="current_reading_value"/>
        <result property="consumptionVolume" column="consumption_volume"/>
        <result property="baseChargeAmount" column="base_charge_amount"/>
        <result property="adjustmentsAmount" column="adjustments_amount"/>
        <result property="totalAmount" column="total_amount"/>
        <result property="billStatus" column="bill_status"/>
        <result property="billingDueDate" column="billing_due_date"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>

    <!-- 优化的批量获取账单信息查询 -->
    <select id="batchSelectBillsByIds" resultMap="WaterfeeBillVoResult">
        SELECT
            b.bill_id, b.bill_number, b.customer_id, b.meter_id, b.meter_book_id,
            b.billing_period_start, b.billing_period_end, b.previous_reading_value, b.current_reading_value,
            b.consumption_volume, b.base_charge_amount, b.adjustments_amount,
            b.total_amount, b.bill_status, b.billing_due_date, b.create_time, b.update_time,
            b.remark, b.del_flag, b.tenant_id
        FROM waterfee_bills b
        WHERE b.bill_id IN
        <foreach collection="billIds" item="billId" open="(" separator="," close=")">
            #{billId}
        </foreach>
        AND b.del_flag = '0'
        ORDER BY b.bill_id
    </select>

    <!-- 批量计算账单统计信息 -->
    <select id="batchCalculateBillStatistics" resultType="java.util.Map">
        SELECT
            b.customer_id,
            COUNT(*) as total_bills,
            COUNT(CASE WHEN b.bill_status = 'PAID' THEN 1 END) as paid_bills,
            COUNT(CASE WHEN b.bill_status = 'DRAFT' THEN 1 END) as draft_bills,
            COUNT(CASE WHEN b.bill_status = 'ISSUED' THEN 1 END) as issued_bills,
            COUNT(CASE WHEN b.bill_status = 'OVERDUE' THEN 1 END) as overdue_bills,
            COALESCE(SUM(b.total_amount), 0) as total_amount,
            COALESCE(SUM(b.amount_paid), 0) as total_paid,
            COALESCE(SUM(b.balance_due), 0) as total_balance_due,
            COALESCE(SUM(b.consumption_volume), 0) as total_consumption,
            COALESCE(AVG(b.total_amount), 0) as avg_bill_amount,
            MAX(b.billing_due_date) as latest_due_date,
            MIN(b.billing_period_start) as earliest_period_start,
            MAX(b.billing_period_end) as latest_period_end
        FROM waterfee_bills b
        WHERE b.customer_id IN
        <foreach collection="customerIds" item="customerId" open="(" separator="," close=")">
            #{customerId}
        </foreach>
        AND b.del_flag = '0'
        GROUP BY b.customer_id
        ORDER BY b.customer_id
    </select>

    <!-- 批量获取未支付账单 -->
    <select id="batchSelectUnpaidBillsByCustomers" resultMap="WaterfeeBillVoResult">
        SELECT bill_id, bill_number, customer_id, total_amount, bill_status
        FROM waterfee_bills
        WHERE customer_id IN
        <foreach collection="customerIds" item="customerId" open="(" separator="," close=")">
            #{customerId}
        </foreach>
        AND bill_status = '0'
        AND del_flag = '0'
        ORDER BY customer_id, billing_due_date ASC
    </select>

    <!-- 批量获取客户余额信息 -->
    <select id="batchSelectCustomerBalances" resultType="java.util.Map">
        SELECT
            user_id as customer_id,
            balance as account_balance,
            0 as frozen_amount,
            balance as available_balance
        FROM waterfee_user
        WHERE user_id IN
        <foreach collection="customerIds" item="customerId" open="(" separator="," close=")">
            #{customerId}
        </foreach>
        AND del_flag = '0'
    </select>

    <!-- 批量获取账单统计信息 -->
    <select id="batchGetBillStatistics" resultType="java.util.Map">
        SELECT
            b.meter_id,
            COUNT(*) as bill_count,
            SUM(b.total_amount) as total_amount,
            SUM(CASE WHEN b.bill_status = '1' THEN 1 ELSE 0 END) as paid_count,
            SUM(CASE WHEN b.bill_status = '0' THEN 1 ELSE 0 END) as unpaid_count,
            AVG(b.total_amount) as avg_amount,
            MAX(b.total_amount) as max_amount,
            MIN(b.total_amount) as min_amount
        FROM waterfee_bills b
        LEFT JOIN waterfee_meter m ON b.meter_id = m.meter_id
        WHERE m.meter_no IN
        <foreach collection="meterNos" item="meterNo" open="(" separator="," close=")">
            #{meterNo}
        </foreach>
        AND b.create_time BETWEEN #{startDate} AND #{endDate}
        AND b.del_flag = '0'
        GROUP BY b.meter_id
        ORDER BY b.meter_id
    </select>

    <!-- 批量获取客户支付能力信息 -->
    <select id="batchGetCustomerPaymentCapacity" resultType="java.util.Map">
        SELECT
            u.user_id as customer_id,
            u.balance as account_balance,
            0 as frozen_amount,
            u.balance as available_balance,
            COALESCE(ub.unpaid_amount, 0) as unpaid_amount,
            COALESCE(ub.unpaid_count, 0) as unpaid_count,
            CASE
                WHEN u.balance >= COALESCE(ub.unpaid_amount, 0)
                THEN 1 ELSE 0
            END as can_pay_all
        FROM waterfee_user u
        LEFT JOIN (
            SELECT
                customer_id,
                SUM(total_amount) as unpaid_amount,
                COUNT(*) as unpaid_count
            FROM waterfee_bills
            WHERE customer_id IN
            <foreach collection="customerIds" item="customerId" open="(" separator="," close=")">
                #{customerId}
            </foreach>
            AND bill_status = '0'
            AND del_flag = '0'
            GROUP BY customer_id
        ) ub ON u.user_id = ub.customer_id
        WHERE u.user_id IN
        <foreach collection="customerIds" item="customerId" open="(" separator="," close=")">
            #{customerId}
        </foreach>
        AND u.del_flag = '0'
        ORDER BY u.user_id
    </select>

    <!-- 批量获取账单详细信息（包含客户信息） -->
    <select id="batchGetBillDetailsWithCustomer" resultType="java.util.Map">
        SELECT
            b.bill_id,
            b.bill_number,
            b.customer_id,
            b.meter_id,
            b.total_amount,
            b.bill_status,
            b.billing_due_date,
            u.user_name as customer_name,
            u.phone_number as contact_phone,
            u.address as customer_address,
            u.balance as account_balance,
            0 as frozen_amount,
            u.balance as available_balance
        FROM waterfee_bills b
        LEFT JOIN waterfee_user u ON b.customer_id = u.user_id AND u.del_flag = '0'
        WHERE b.bill_id IN
        <foreach collection="billIds" item="billId" open="(" separator="," close=")">
            #{billId}
        </foreach>
        AND b.del_flag = '0'
        ORDER BY b.bill_id
    </select>

    <!-- 批量获取最近的支付记录 -->
    <select id="batchGetRecentPaymentRecords" resultType="java.util.Map">
        SELECT * FROM (
            SELECT
                pd.user_id as customer_id,
                pd.bill_id,
                pd.payment_amount,
                pd.payment_method,
                pd.payment_time,
                pd.payment_status,
                pd.transaction_id,
                ROW_NUMBER() OVER (PARTITION BY pd.user_id ORDER BY pd.payment_time DESC) as rn
            FROM waterfee_payment_detail pd
            WHERE pd.user_id IN
            <foreach collection="customerIds" item="customerId" open="(" separator="," close=")">
                #{customerId}
            </foreach>
            AND pd.del_flag = '0'
        ) ranked
        WHERE ranked.rn &lt;= #{limit}
        ORDER BY ranked.customer_id, ranked.payment_time DESC
    </select>

    <!-- 批量获取账单支付历史 -->
    <select id="batchGetBillPaymentHistory" resultType="java.util.Map">
        SELECT
            pd.bill_id,
            pd.payment_amount,
            pd.payment_time,
            pd.payment_method,
            pd.payment_status,
            pd.transaction_id,
            pd.remark
        FROM waterfee_payment_detail pd
        WHERE pd.bill_id IN
        <foreach collection="billIds" item="billId" open="(" separator="," close=")">
            #{billId}
        </foreach>
        AND pd.del_flag = '0'
        ORDER BY pd.bill_id, pd.payment_time DESC
    </select>

    <!-- 批量检查账单是否存在 -->
    <select id="batchCheckBillExists" resultType="java.lang.String">
        SELECT DISTINCT meter_no
        FROM waterfee_bill
        WHERE meter_no IN
        <foreach collection="meterNos" item="meterNo" open="(" separator="," close=")">
            #{meterNo}
        </foreach>
        AND billing_period_start = #{billingPeriod}
        AND del_flag = '0'
    </select>

    <!-- 批量检查支付能力 -->
    <select id="batchCheckPaymentCapacity" resultType="java.util.Map">
        SELECT
            u.user_id as customer_id,
            u.balance as account_balance,
            0 as frozen_amount,
            u.balance as available_balance
        FROM waterfee_user u
        WHERE u.user_id IN
        <foreach collection="paymentRequests" item="request" open="(" separator="," close=")">
            #{request.customerId}
        </foreach>
        AND u.del_flag = '0'
        ORDER BY u.user_id
    </select>

    <!-- 性能优化的索引建议 -->
    <!--
    建议创建以下索引来优化账单处理性能：

    1. 账单表索引：
    CREATE INDEX idx_bills_customer_status ON waterfee_bills(customer_id, bill_status);
    CREATE INDEX idx_bills_meter_period ON waterfee_bills(meter_id, billing_period_start);
    CREATE INDEX idx_bills_status_due ON waterfee_bills(bill_status, billing_due_date);

    2. 用户表索引：
    CREATE INDEX idx_user_balance ON waterfee_user(user_id, balance);

    3. 支付记录表索引：
    CREATE INDEX idx_payment_user_time ON waterfee_payment_detail(user_id, payment_time DESC);
    CREATE INDEX idx_payment_bill_time ON waterfee_payment_detail(bill_id, payment_time DESC);

    4. 复合索引：
    CREATE INDEX idx_bills_del_status ON waterfee_bills(del_flag, bill_status, customer_id);
    CREATE INDEX idx_user_del_balance ON waterfee_user(del_flag, user_id, balance);
    CREATE INDEX idx_payment_del_status ON waterfee_payment_detail(del_flag, payment_status);
    -->

</mapper>
