# =====================================================
# Redis单机模式配置
# 版本: 1.0.0
# 创建时间: 2025-08-04
# 说明: Redis单机模式配置，避免与哨兵/集群模式冲突
# =====================================================

spring:
  data:
    redis:
      # 单机Redis配置（与Nacos配置保持一致）
      host: ${REDIS_HOST:***********}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:zdscRedis2022}
      database: ${REDIS_DATABASE:0}
      # 需要使用数字（毫秒）
      timeout: ${REDIS_TIMEOUT:10000}
      ssl:
        enabled: ${REDIS_SSL_ENABLED:false}

      # 连接池配置
      lettuce:
        pool:
          max-active: ${REDIS_MAX_ACTIVE:50}
          max-idle: ${REDIS_MAX_IDLE:20}
          min-idle: ${REDIS_MIN_IDLE:5}
          max-wait: ${REDIS_MAX_WAIT:3000}
        shutdown-timeout: 100

      # 连接配置
      connect-timeout: ${REDIS_CONNECT_TIMEOUT:3000}

# Redisson单机配置（与Nacos配置保持一致）
redisson:
  # redis key前缀
  keyPrefix: ${REDISSON_KEY_PREFIX:}
  # 线程池数量
  threads: ${REDISSON_THREADS:4}
  # Netty线程池数量
  nettyThreads: ${REDISSON_NETTY_THREADS:8}

  # 单节点配置
  singleServerConfig:
    # 客户端名称
    clientName: ${spring.application.name:waterfee-performance-optimized}
    # 最小空闲连接数
    connectionMinimumIdleSize: ${REDISSON_MIN_IDLE:8}
    # 连接池大小
    connectionPoolSize: ${REDISSON_POOL_SIZE:32}
    # 连接空闲超时，单位：毫秒
    idleConnectionTimeout: ${REDISSON_IDLE_TIMEOUT:10000}
    # 命令等待超时，单位：毫秒
    timeout: ${REDISSON_COMMAND_TIMEOUT:3000}
    # 发布和订阅连接池大小
    subscriptionConnectionPoolSize: ${REDISSON_SUB_POOL_SIZE:50}
