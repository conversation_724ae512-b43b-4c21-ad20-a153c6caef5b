package org.dromara.waterfee.common.cache;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.redis.utils.RedisUtils;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * 高级缓存管理器
 * 提供多级缓存、智能预热、缓存穿透防护等高级功能
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AdvancedCacheManager {

    // 多级缓存配置
    private static final String CACHE_PREFIX = "waterfee:advanced:";
    private static final Duration DEFAULT_TTL = Duration.ofHours(1);
    private static final int MAX_LOCAL_CACHE_SIZE = 10000;
    
    // 本地缓存（L1）
    private final Map<String, CacheEntry> localCache = new ConcurrentHashMap<>();
    private final ReentrantReadWriteLock cacheLock = new ReentrantReadWriteLock();
    
    // 缓存统计
    private final Map<String, CacheStats> cacheStats = new ConcurrentHashMap<>();
    
    // 缓存穿透防护
    private final Set<String> nullValueCache = ConcurrentHashMap.newKeySet();
    
    /**
     * 缓存条目
     */
    private static class CacheEntry {
        private final Object value;
        private final long timestamp;
        private final Duration ttl;
        
        public CacheEntry(Object value, Duration ttl) {
            this.value = value;
            this.timestamp = System.currentTimeMillis();
            this.ttl = ttl;
        }
        
        public boolean isExpired() {
            return System.currentTimeMillis() - timestamp > ttl.toMillis();
        }
        
        public Object getValue() {
            return value;
        }
    }
    
    /**
     * 缓存统计信息
     */
    public static class CacheStats {
        private long hits = 0;
        private long misses = 0;
        private long l1Hits = 0;
        private long l2Hits = 0;
        
        public void recordHit(boolean isL1) {
            hits++;
            if (isL1) {
                l1Hits++;
            } else {
                l2Hits++;
            }
        }
        
        public void recordMiss() {
            misses++;
        }
        
        public double getHitRate() {
            long total = hits + misses;
            return total > 0 ? (double) hits / total : 0.0;
        }
        
        // Getters
        public long getHits() { return hits; }
        public long getMisses() { return misses; }
        public long getL1Hits() { return l1Hits; }
        public long getL2Hits() { return l2Hits; }
    }
    
    /**
     * 多级缓存获取
     */
    @SuppressWarnings("unchecked")
    public <T> T get(String key, Class<T> type) {
        String fullKey = CACHE_PREFIX + key;
        CacheStats stats = cacheStats.computeIfAbsent(key, k -> new CacheStats());
        
        // L1缓存查找
        cacheLock.readLock().lock();
        try {
            CacheEntry entry = localCache.get(fullKey);
            if (entry != null && !entry.isExpired()) {
                stats.recordHit(true);
                log.debug("L1缓存命中: {}", key);
                return (T) entry.getValue();
            }
        } finally {
            cacheLock.readLock().unlock();
        }
        
        // L2缓存查找（Redis）
        try {
            T value = RedisUtils.getCacheObject(fullKey);
            if (value != null) {
                // 回写到L1缓存
                putToLocalCache(fullKey, value, DEFAULT_TTL);
                stats.recordHit(false);
                log.debug("L2缓存命中: {}", key);
                return value;
            }
        } catch (Exception e) {
            log.warn("Redis缓存访问失败: {}", key, e);
        }
        
        // 缓存未命中
        stats.recordMiss();
        log.debug("缓存未命中: {}", key);
        return null;
    }
    
    /**
     * 多级缓存存储
     */
    public <T> void put(String key, T value, Duration ttl) {
        String fullKey = CACHE_PREFIX + key;
        
        // 存储到L1缓存
        putToLocalCache(fullKey, value, ttl);
        
        // 存储到L2缓存（Redis）
        try {
            RedisUtils.setCacheObject(fullKey, value, ttl);
            log.debug("缓存存储成功: {}", key);
        } catch (Exception e) {
            log.error("Redis缓存存储失败: {}", key, e);
        }
    }
    
    /**
     * 存储到本地缓存
     */
    private void putToLocalCache(String fullKey, Object value, Duration ttl) {
        cacheLock.writeLock().lock();
        try {
            // 检查缓存大小限制
            if (localCache.size() >= MAX_LOCAL_CACHE_SIZE) {
                evictOldestEntries();
            }
            
            localCache.put(fullKey, new CacheEntry(value, ttl));
        } finally {
            cacheLock.writeLock().unlock();
        }
    }
    
    /**
     * 淘汰最旧的缓存条目
     */
    private void evictOldestEntries() {
        int evictCount = MAX_LOCAL_CACHE_SIZE / 4; // 淘汰25%
        List<Map.Entry<String, CacheEntry>> entries = new ArrayList<>(localCache.entrySet());
        
        // 按时间戳排序，淘汰最旧的
        entries.sort(Comparator.comparing(e -> e.getValue().timestamp));
        
        for (int i = 0; i < evictCount && i < entries.size(); i++) {
            localCache.remove(entries.get(i).getKey());
        }
        
        log.debug("淘汰本地缓存条目: {}", evictCount);
    }
    
    /**
     * 缓存预热
     */
    public <T> void warmUp(String keyPattern, Map<String, T> data, Duration ttl) {
        log.info("开始缓存预热，模式: {}, 数据量: {}", keyPattern, data.size());
        
        long startTime = System.currentTimeMillis();
        int successCount = 0;
        
        for (Map.Entry<String, T> entry : data.entrySet()) {
            try {
                put(entry.getKey(), entry.getValue(), ttl);
                successCount++;
            } catch (Exception e) {
                log.warn("缓存预热失败: {}", entry.getKey(), e);
            }
        }
        
        long duration = System.currentTimeMillis() - startTime;
        log.info("缓存预热完成，成功: {}/{}, 耗时: {}ms", successCount, data.size(), duration);
    }
    
    /**
     * 批量获取
     */
    @SuppressWarnings("unchecked")
    public <T> Map<String, T> batchGet(Collection<String> keys, Class<T> type) {
        Map<String, T> result = new HashMap<>();
        List<String> missedKeys = new ArrayList<>();
        
        // 先从本地缓存获取
        cacheLock.readLock().lock();
        try {
            for (String key : keys) {
                String fullKey = CACHE_PREFIX + key;
                CacheEntry entry = localCache.get(fullKey);
                if (entry != null && !entry.isExpired()) {
                    result.put(key, (T) entry.getValue());
                } else {
                    missedKeys.add(key);
                }
            }
        } finally {
            cacheLock.readLock().unlock();
        }
        
        // 从Redis批量获取未命中的键
        if (!missedKeys.isEmpty()) {
            try {
                for (String key : missedKeys) {
                    String fullKey = CACHE_PREFIX + key;
                    T value = RedisUtils.getCacheObject(fullKey);
                    if (value != null) {
                        result.put(key, value);
                        putToLocalCache(fullKey, value, DEFAULT_TTL);
                    }
                }
            } catch (Exception e) {
                log.warn("批量Redis缓存访问失败", e);
            }
        }
        
        log.debug("批量获取缓存，请求: {}, 命中: {}", keys.size(), result.size());
        return result;
    }
    
    /**
     * 删除缓存
     */
    public void evict(String key) {
        String fullKey = CACHE_PREFIX + key;
        
        // 从本地缓存删除
        cacheLock.writeLock().lock();
        try {
            localCache.remove(fullKey);
        } finally {
            cacheLock.writeLock().unlock();
        }
        
        // 从Redis删除
        try {
            RedisUtils.deleteObject(fullKey);
            log.debug("缓存删除成功: {}", key);
        } catch (Exception e) {
            log.error("Redis缓存删除失败: {}", key, e);
        }
    }
    
    /**
     * 批量删除缓存
     */
    public void batchEvict(Collection<String> keys) {
        List<String> fullKeys = keys.stream()
                .map(key -> CACHE_PREFIX + key)
                .toList();
        
        // 从本地缓存批量删除
        cacheLock.writeLock().lock();
        try {
            fullKeys.forEach(localCache::remove);
        } finally {
            cacheLock.writeLock().unlock();
        }
        
        // 从Redis批量删除
        try {
            RedisUtils.deleteObject(fullKeys);
            log.debug("批量缓存删除成功，数量: {}", keys.size());
        } catch (Exception e) {
            log.error("批量Redis缓存删除失败", e);
        }
    }
    
    /**
     * 清理过期的本地缓存
     */
    public void cleanupExpiredEntries() {
        cacheLock.writeLock().lock();
        try {
            Iterator<Map.Entry<String, CacheEntry>> iterator = localCache.entrySet().iterator();
            int cleanupCount = 0;
            
            while (iterator.hasNext()) {
                Map.Entry<String, CacheEntry> entry = iterator.next();
                if (entry.getValue().isExpired()) {
                    iterator.remove();
                    cleanupCount++;
                }
            }
            
            if (cleanupCount > 0) {
                log.debug("清理过期本地缓存条目: {}", cleanupCount);
            }
        } finally {
            cacheLock.writeLock().unlock();
        }
    }
    
    /**
     * 获取缓存统计信息
     */
    public Map<String, CacheStats> getCacheStatistics() {
        return new HashMap<>(cacheStats);
    }
    
    /**
     * 获取缓存状态信息
     */
    public Map<String, Object> getCacheStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("localCacheSize", localCache.size());
        status.put("maxLocalCacheSize", MAX_LOCAL_CACHE_SIZE);
        status.put("cacheStatsCount", cacheStats.size());
        
        // 计算总体命中率
        long totalHits = cacheStats.values().stream().mapToLong(CacheStats::getHits).sum();
        long totalMisses = cacheStats.values().stream().mapToLong(CacheStats::getMisses).sum();
        double overallHitRate = (totalHits + totalMisses) > 0 ? 
                (double) totalHits / (totalHits + totalMisses) : 0.0;
        
        status.put("overallHitRate", overallHitRate);
        status.put("totalHits", totalHits);
        status.put("totalMisses", totalMisses);
        
        return status;
    }
}
