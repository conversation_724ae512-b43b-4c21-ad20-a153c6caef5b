package org.dromara.waterfee.common.controller;

import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.web.core.BaseController;
import org.dromara.waterfee.common.cache.AdvancedCacheManager;
import org.dromara.waterfee.common.cache.SmartCalculationCacheService;
import org.dromara.waterfee.common.monitor.PerformanceMonitorService;
import org.dromara.waterfee.common.parallel.ParallelProcessingEngine;
import org.dromara.waterfee.common.service.BillBatchOptimizationService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 性能优化管理控制器
 * 提供性能监控、缓存管理、批量处理等功能的API接口
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
@RestController
@RequestMapping("/waterfee/performance")
@RequiredArgsConstructor
public class PerformanceOptimizationController extends BaseController {

    private final PerformanceMonitorService performanceMonitorService;
    private final AdvancedCacheManager advancedCacheManager;
    private final SmartCalculationCacheService calculationCacheService;
    private final ParallelProcessingEngine parallelProcessingEngine;
    private final BillBatchOptimizationService batchOptimizationService;

    /**
     * 获取性能监控概览
     */
    @GetMapping("/overview")
    @PreAuthorize("@ss.hasPermi('waterfee:performance:view')")
    public R<Map<String, Object>> getPerformanceOverview() {
        Map<String, Object> overview = new HashMap<>();
        
        // 性能摘要
        overview.put("performanceSummary", performanceMonitorService.getPerformanceSummary());
        
        // 缓存状态
        overview.put("cacheStatus", advancedCacheManager.getCacheStatus());
        
        // 计算缓存效率
        overview.put("calculationCacheEfficiency", calculationCacheService.getCacheEfficiencyReport());
        
        // 线程池状态
        overview.put("threadPoolStatus", parallelProcessingEngine.getThreadPoolStatus());
        
        // 批量操作统计
        overview.put("batchOperationStats", batchOptimizationService.getBatchOperationStats());
        
        return R.ok(overview);
    }

    /**
     * 获取性能指标历史数据
     */
    @GetMapping("/metrics/{metricName}")
    @PreAuthorize("@ss.hasPermi('waterfee:performance:view')")
    public R<List<PerformanceMonitorService.PerformanceMetric>> getMetricHistory(
            @PathVariable String metricName) {
        List<PerformanceMonitorService.PerformanceMetric> history = 
                performanceMonitorService.getMetricHistory(metricName);
        return R.ok(history);
    }

    /**
     * 获取活跃告警
     */
    @GetMapping("/alerts")
    @PreAuthorize("@ss.hasPermi('waterfee:performance:view')")
    public R<Map<String, PerformanceMonitorService.PerformanceAlert>> getActiveAlerts() {
        Map<String, PerformanceMonitorService.PerformanceAlert> alerts = 
                performanceMonitorService.getActiveAlerts();
        return R.ok(alerts);
    }

    /**
     * 获取缓存统计信息
     */
    @GetMapping("/cache/stats")
    @PreAuthorize("@ss.hasPermi('waterfee:performance:view')")
    public R<Map<String, Object>> getCacheStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        // 高级缓存统计
        stats.put("advancedCache", advancedCacheManager.getCacheStatistics());
        
        // 计算缓存统计
        stats.put("calculationCache", calculationCacheService.getCalculationStatistics());
        
        return R.ok(stats);
    }

    /**
     * 清理缓存
     */
    @PostMapping("/cache/cleanup")
    @PreAuthorize("@ss.hasPermi('waterfee:performance:manage')")
    @Log(title = "性能优化", businessType = BusinessType.UPDATE)
    public R<Void> cleanupCache(@RequestParam(required = false) String cacheType) {
        if ("advanced".equals(cacheType)) {
            advancedCacheManager.cleanupExpiredEntries();
        } else if ("calculation".equals(cacheType)) {
            // 清理计算缓存的过期条目
            // calculationCacheService.cleanupExpiredEntries();
        } else {
            // 清理所有缓存
            advancedCacheManager.cleanupExpiredEntries();
        }
        
        return R.ok();
    }

    /**
     * 预热缓存
     */
    @PostMapping("/cache/warmup")
    @PreAuthorize("@ss.hasPermi('waterfee:performance:manage')")
    @Log(title = "性能优化", businessType = BusinessType.UPDATE)
    public R<Void> warmupCache() {
        // 预热批量处理缓存
        batchOptimizationService.preWarmBatchProcessingCache();
        
        return R.ok();
    }

    /**
     * 获取并行处理统计
     */
    @GetMapping("/parallel/stats")
    @PreAuthorize("@ss.hasPermi('waterfee:performance:view')")
    public R<Map<String, ParallelProcessingEngine.ProcessingStats>> getParallelProcessingStats() {
        Map<String, ParallelProcessingEngine.ProcessingStats> stats = 
                parallelProcessingEngine.getProcessingStatistics();
        return R.ok(stats);
    }

    /**
     * 重置并行处理统计
     */
    @PostMapping("/parallel/reset-stats")
    @PreAuthorize("@ss.hasPermi('waterfee:performance:manage')")
    @Log(title = "性能优化", businessType = BusinessType.UPDATE)
    public R<Void> resetParallelProcessingStats() {
        parallelProcessingEngine.resetStatistics();
        return R.ok();
    }

    /**
     * 获取批量操作详细统计
     */
    @GetMapping("/batch/detailed-stats")
    @PreAuthorize("@ss.hasPermi('waterfee:performance:view')")
    public R<Map<String, Object>> getDetailedBatchStats() {
        Map<String, Object> detailedStats = batchOptimizationService.getBatchOperationStats();
        return R.ok(detailedStats);
    }

    /**
     * 执行性能测试
     */
    @PostMapping("/test")
    @PreAuthorize("@ss.hasPermi('waterfee:performance:manage')")
    @Log(title = "性能优化", businessType = BusinessType.OTHER)
    public R<Map<String, Object>> performanceTest(
            @RequestParam(defaultValue = "1000") int dataSize,
            @RequestParam(defaultValue = "batch_query") String testType) {
        
        Map<String, Object> testResult = new HashMap<>();
        long startTime = System.currentTimeMillis();
        
        try {
            switch (testType) {
                case "cache_performance":
                    testResult = testCachePerformance(dataSize);
                    break;
                case "parallel_processing":
                    testResult = testParallelProcessing(dataSize);
                    break;
                case "batch_query":
                default:
                    testResult = testBatchQuery(dataSize);
                    break;
            }
            
            long duration = System.currentTimeMillis() - startTime;
            testResult.put("totalDuration", duration);
            testResult.put("testType", testType);
            testResult.put("dataSize", dataSize);
            
        } catch (Exception e) {
            testResult.put("error", e.getMessage());
            testResult.put("success", false);
        }
        
        return R.ok(testResult);
    }

    /**
     * 测试缓存性能
     */
    private Map<String, Object> testCachePerformance(int dataSize) {
        Map<String, Object> result = new HashMap<>();
        
        // 模拟缓存性能测试
        long cacheWriteStart = System.currentTimeMillis();
        for (int i = 0; i < dataSize; i++) {
            advancedCacheManager.put("test_key_" + i, "test_value_" + i, 
                    java.time.Duration.ofMinutes(5));
        }
        long cacheWriteTime = System.currentTimeMillis() - cacheWriteStart;
        
        long cacheReadStart = System.currentTimeMillis();
        int hitCount = 0;
        for (int i = 0; i < dataSize; i++) {
            String value = advancedCacheManager.get("test_key_" + i, String.class);
            if (value != null) {
                hitCount++;
            }
        }
        long cacheReadTime = System.currentTimeMillis() - cacheReadStart;
        
        result.put("cacheWriteTime", cacheWriteTime);
        result.put("cacheReadTime", cacheReadTime);
        result.put("hitCount", hitCount);
        result.put("hitRate", (double) hitCount / dataSize * 100);
        result.put("success", true);
        
        return result;
    }

    /**
     * 测试并行处理性能
     */
    private Map<String, Object> testParallelProcessing(int dataSize) {
        Map<String, Object> result = new HashMap<>();
        
        // 创建测试数据
        List<Integer> testData = new java.util.ArrayList<>();
        for (int i = 0; i < dataSize; i++) {
            testData.add(i);
        }
        
        // 并行处理测试
        ParallelProcessingEngine.ProcessingResult<String> processingResult = 
                parallelProcessingEngine.processInParallel(
                        "performance_test",
                        testData,
                        i -> "processed_" + i
                );
        
        result.put("totalTasks", processingResult.getTotalTasks());
        result.put("successCount", processingResult.getSuccessCount());
        result.put("errorCount", processingResult.getErrorCount());
        result.put("successRate", processingResult.getSuccessRate() * 100);
        result.put("processingTime", processingResult.getTotalProcessingTime());
        result.put("success", true);
        
        return result;
    }

    /**
     * 测试批量查询性能
     */
    private Map<String, Object> testBatchQuery(int dataSize) {
        Map<String, Object> result = new HashMap<>();
        
        // 创建测试ID列表
        List<Long> testIds = new java.util.ArrayList<>();
        for (int i = 1; i <= dataSize; i++) {
            testIds.add((long) i);
        }
        
        // 批量查询测试
        long queryStart = System.currentTimeMillis();
        List<org.dromara.waterfee.bill.domain.WaterfeeBill> bills = 
                batchOptimizationService.batchQueryBills(testIds);
        long queryTime = System.currentTimeMillis() - queryStart;
        
        result.put("queryTime", queryTime);
        result.put("requestedCount", testIds.size());
        result.put("returnedCount", bills.size());
        result.put("averageTimePerItem", (double) queryTime / testIds.size());
        result.put("success", true);
        
        return result;
    }

    /**
     * 获取系统资源使用情况
     */
    @GetMapping("/system/resources")
    @PreAuthorize("@ss.hasPermi('waterfee:performance:view')")
    public R<Map<String, Object>> getSystemResources() {
        Map<String, Object> resources = new HashMap<>();
        
        // JVM内存信息
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        
        Map<String, Object> memory = new HashMap<>();
        memory.put("maxMemory", maxMemory / 1024 / 1024); // MB
        memory.put("totalMemory", totalMemory / 1024 / 1024); // MB
        memory.put("usedMemory", usedMemory / 1024 / 1024); // MB
        memory.put("freeMemory", freeMemory / 1024 / 1024); // MB
        memory.put("usagePercentage", (double) usedMemory / maxMemory * 100);
        
        resources.put("memory", memory);
        
        // 线程信息
        Map<String, Object> threads = new HashMap<>();
        threads.put("activeCount", Thread.activeCount());
        threads.put("totalStarted", java.lang.management.ManagementFactory.getThreadMXBean().getTotalStartedThreadCount());
        
        resources.put("threads", threads);
        
        // 处理器信息
        Map<String, Object> processors = new HashMap<>();
        processors.put("availableProcessors", runtime.availableProcessors());
        
        resources.put("processors", processors);
        
        return R.ok(resources);
    }
}
