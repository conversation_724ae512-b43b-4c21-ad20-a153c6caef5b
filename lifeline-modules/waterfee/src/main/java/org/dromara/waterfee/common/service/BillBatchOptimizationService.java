package org.dromara.waterfee.common.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.waterfee.bill.domain.WaterfeeBill;
import org.dromara.waterfee.bill.mapper.WaterfeeBillMapper;
import org.dromara.waterfee.bill.service.IWaterfeeBillService;
import org.dromara.waterfee.common.cache.AdvancedCacheManager;
import org.dromara.waterfee.common.cache.BillPerformanceCacheService;
import org.dromara.waterfee.common.cache.SmartCalculationCacheService;
import org.dromara.waterfee.common.parallel.ParallelProcessingEngine;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 账单批量操作优化服务
 * 提供高效的批量账单处理功能
 *
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BillBatchOptimizationService {

    private final WaterfeeBillMapper billMapper;
    private final IWaterfeeBillService billService;
    private final BillPerformanceCacheService cacheService;
    private final AdvancedCacheManager advancedCacheManager;
    private final SmartCalculationCacheService calculationCacheService;
    private final ParallelProcessingEngine parallelProcessingEngine;

    /**
     * 批量更新账单状态（优化版本）
     *
     * @param billIds 账单ID集合
     * @param newStatus 新状态
     * @param updateBy 更新人
     * @return 更新成功的数量
     */
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdateBillStatus(Collection<Long> billIds, String newStatus, Long updateBy) {
        if (billIds == null || billIds.isEmpty()) {
            return 0;
        }

        long startTime = System.currentTimeMillis();

        try {
            // 使用批量更新SQL，避免循环单条更新
            int updateCount = billMapper.batchUpdateBillStatus(billIds, newStatus, updateBy, new Date());

            long endTime = System.currentTimeMillis();
            log.info("批量更新账单状态完成，数量: {}, 耗时: {}ms", updateCount, endTime - startTime);

            return updateCount;
        } catch (Exception e) {
            log.error("批量更新账单状态失败", e);
            throw e;
        }
    }

    /**
     * 批量查询账单信息（优化版本）
     *
     * @param billIds 账单ID集合
     * @return 账单信息列表
     */
    public List<WaterfeeBill> batchQueryBills(Collection<Long> billIds) {
        if (billIds == null || billIds.isEmpty()) {
            return List.of();
        }

        // 生成批量查询缓存键
        String cacheKey = cacheService.generateBatchQueryKey("bills", billIds.toString());

        // 尝试从缓存获取
        @SuppressWarnings("unchecked")
        List<WaterfeeBill> cachedResult = cacheService.getCachedBatchQueryResult(cacheKey, List.class);
        if (cachedResult != null) {
            log.debug("从缓存获取批量账单查询结果，数量: {}", cachedResult.size());
            return cachedResult;
        }

        long startTime = System.currentTimeMillis();

        try {
            // 使用批量查询，避免N+1查询问题
            List<WaterfeeBill> bills = billMapper.batchSelectBillsByIds(billIds);

            // 缓存查询结果
            cacheService.cacheBatchQueryResult(cacheKey, bills);

            long endTime = System.currentTimeMillis();
            log.info("批量查询账单完成，数量: {}, 耗时: {}ms", bills.size(), endTime - startTime);

            return bills;
        } catch (Exception e) {
            log.error("批量查询账单失败", e);
            throw e;
        }
    }

    /**
     * 批量获取账单详细信息（包含客户信息）
     *
     * @param billIds 账单ID集合
     * @return 账单详细信息Map
     */
    public List<Map<String, Object>> batchGetBillDetailsWithCustomer(Collection<Long> billIds) {
        if (billIds == null || billIds.isEmpty()) {
            return List.of();
        }

        // 生成批量查询缓存键
        String cacheKey = cacheService.generateBatchQueryKey("bill_details", billIds.toString());

        // 尝试从缓存获取
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> cachedResult = cacheService.getCachedBatchQueryResult(cacheKey, List.class);
        if (cachedResult != null) {
            log.debug("从缓存获取批量账单详情查询结果，数量: {}", cachedResult.size());
            return cachedResult;
        }

        long startTime = System.currentTimeMillis();

        try {
            // 使用优化的批量查询SQL
            List<Map<String, Object>> billDetails = billMapper.batchGetBillDetailsWithCustomer(billIds);

            // 缓存查询结果
            cacheService.cacheBatchQueryResult(cacheKey, billDetails);

            long endTime = System.currentTimeMillis();
            log.info("批量获取账单详情完成，数量: {}, 耗时: {}ms", billDetails.size(), endTime - startTime);

            return billDetails;
        } catch (Exception e) {
            log.error("批量获取账单详情失败", e);
            throw e;
        }
    }

    /**
     * 批量发行账单（优化版本）
     *
     * @param meterBookIds 表册ID集合
     * @return 发行成功的账单数量
     */
    @Transactional(rollbackFor = Exception.class)
    public int batchIssueBills(Collection<Long> meterBookIds) {
        if (meterBookIds == null || meterBookIds.isEmpty()) {
            return 0;
        }

        long startTime = System.currentTimeMillis();

        try {
            // 使用批量更新SQL，提升性能
            int updateCount = billMapper.batchIssueBillsByMeterBookIds(meterBookIds, new Date());

            long endTime = System.currentTimeMillis();
            log.info("批量发行账单完成，表册数量: {}, 账单数量: {}, 耗时: {}ms",
                    meterBookIds.size(), updateCount, endTime - startTime);

            return updateCount;
        } catch (Exception e) {
            log.error("批量发行账单失败", e);
            throw e;
        }
    }

    /**
     * 批量计算账单统计信息
     *
     * @param customerIds 客户ID集合
     * @return 统计信息Map
     */
    public Map<Long, Map<String, Object>> batchCalculateBillStatistics(Collection<Long> customerIds) {
        if (customerIds == null || customerIds.isEmpty()) {
            return Map.of();
        }

        // 生成批量查询缓存键
        String cacheKey = cacheService.generateBatchQueryKey("bill_stats", customerIds.toString());

        // 尝试从缓存获取
        @SuppressWarnings("unchecked")
        Map<Long, Map<String, Object>> cachedResult = cacheService.getCachedBatchQueryResult(cacheKey, Map.class);
        if (cachedResult != null) {
            log.debug("从缓存获取批量账单统计结果，数量: {}", cachedResult.size());
            return cachedResult;
        }

        long startTime = System.currentTimeMillis();

        try {
            // 方案1：使用List返回，然后转换（推荐）
            List<Map<String, Object>> statisticsList = billMapper.batchCalculateBillStatistics(customerIds);
            Map<Long, Map<String, Object>> statisticsMap = statisticsList.stream()
                    .collect(Collectors.toMap(
                            stat -> (Long) stat.get("customer_id"),
                            stat -> stat
                    ));

            // 方案2：如果遇到@MapKey问题，可以使用这个方法
            // Map<String, Map<String, Object>> rawMap = billMapper.batchCalculateBillStatisticsAsMap(customerIds);
            // Map<Long, Map<String, Object>> statisticsMap = rawMap.entrySet().stream()
            //         .collect(Collectors.toMap(
            //                 entry -> Long.valueOf(entry.getKey()),
            //                 Map.Entry::getValue
            //         ));

            // 缓存查询结果
            cacheService.cacheBatchQueryResult(cacheKey, statisticsMap);

            long endTime = System.currentTimeMillis();
            log.info("批量计算账单统计完成，客户数量: {}, 耗时: {}ms",
                    customerIds.size(), endTime - startTime);

            return statisticsMap;
        } catch (Exception e) {
            log.error("批量计算账单统计失败", e);
            throw e;
        }
    }

    /**
     * 批量删除账单（优化版本）
     *
     * @param billIds 账单ID集合
     * @param isValid 是否进行有效性校验
     * @return 删除成功的数量
     */
    @Transactional(rollbackFor = Exception.class)
    public int batchDeleteBills(Collection<Long> billIds, boolean isValid) {
        if (billIds == null || billIds.isEmpty()) {
            return 0;
        }

        long startTime = System.currentTimeMillis();

        try {
            if (isValid) {
                // 批量校验账单状态
                int invalidCount = billMapper.countInvalidBillsForDeletion(billIds);
                if (invalidCount > 0) {
                    throw new RuntimeException("存在不能删除的账单，数量: " + invalidCount);
                }
            }

            // 使用批量删除（逻辑删除）
            int deleteCount = billMapper.batchLogicalDeleteBills(billIds, new Date());

            long endTime = System.currentTimeMillis();
            log.info("批量删除账单完成，数量: {}, 耗时: {}ms", deleteCount, endTime - startTime);

            return deleteCount;
        } catch (Exception e) {
            log.error("批量删除账单失败", e);
            throw e;
        }
    }

    /**
     * 清理批量查询缓存
     */
    public void clearBatchQueryCache() {
        try {
            // 清理所有批量查询相关的缓存
            cacheService.evictAllBatchQueryCache();
            log.info("清理批量查询缓存完成");
        } catch (Exception e) {
            log.error("清理批量查询缓存失败", e);
        }
    }

    /**
     * 高级并行批量处理账单（阶段二优化）
     *
     * @param billIds 账单ID集合
     * @param processor 处理函数
     * @return 处理结果
     */
    public <T> ParallelProcessingEngine.ProcessingResult<T> advancedParallelProcess(
            Collection<Long> billIds,
            java.util.function.Function<WaterfeeBill, T> processor) {

        if (billIds == null || billIds.isEmpty()) {
            return new ParallelProcessingEngine.ProcessingResult<>(
                    Collections.emptyList(), Collections.emptyList(), 0, 0);
        }

        // 1. 智能批量获取账单数据（使用高级缓存）
        List<WaterfeeBill> bills = smartBatchGetBills(billIds);

        // 2. 使用并行处理引擎处理
        return parallelProcessingEngine.processInParallel(
                "bill_processing", bills, processor);
    }

    /**
     * 智能批量获取账单（使用多级缓存）
     */
    private List<WaterfeeBill> smartBatchGetBills(Collection<Long> billIds) {
        // 1. 尝试从高级缓存批量获取
        Map<String, WaterfeeBill> cachedBills = advancedCacheManager.batchGet(
                billIds.stream().map(id -> "bill:" + id).toList(),
                WaterfeeBill.class);

        // 2. 找出缓存未命中的ID
        Set<Long> missedIds = new HashSet<>(billIds);
        List<WaterfeeBill> result = new ArrayList<>();

        for (Map.Entry<String, WaterfeeBill> entry : cachedBills.entrySet()) {
            String key = entry.getKey();
            Long billId = Long.valueOf(key.substring(5)); // 移除 "bill:" 前缀
            missedIds.remove(billId);
            result.add(entry.getValue());
        }

        // 3. 从数据库获取未命中的数据
        if (!missedIds.isEmpty()) {
            List<WaterfeeBill> dbBills = batchQueryBills(missedIds);
            result.addAll(dbBills);

            // 4. 将数据库查询结果缓存到高级缓存
            Map<String, WaterfeeBill> cacheData = new HashMap<>();
            for (WaterfeeBill bill : dbBills) {
                cacheData.put("bill:" + bill.getBillId(), bill);
            }
            advancedCacheManager.warmUp("bills", cacheData, Duration.ofMinutes(30));
        }

        return result;
    }

    /**
     * 智能账单计算处理（使用计算缓存）
     *
     * @param customerId 客户ID
     * @param meterId 水表ID
     * @param waterUsage 用水量
     * @param priceConfigId 价格配置ID
     * @param billingPeriod 计费周期
     * @return 计算结果
     */
    public SmartCalculationCacheService.BillCalculationResult smartCalculateBill(
            Long customerId, Long meterId, Double waterUsage,
            Long priceConfigId, String billingPeriod) {

        // 生成计算缓存键
        String calcKey = calculationCacheService.generateBillCalcKey(
                customerId, meterId, waterUsage, priceConfigId, billingPeriod);

        // 尝试从缓存获取
        SmartCalculationCacheService.BillCalculationResult cachedResult =
                calculationCacheService.getCachedBillCalculation(calcKey);

        if (cachedResult != null) {
            log.debug("账单计算缓存命中: {}", calcKey);
            return cachedResult;
        }

        // 缓存未命中，执行计算
        long startTime = System.currentTimeMillis();

        // 这里应该调用实际的计算逻辑
        // 为了示例，创建一个模拟结果
        BigDecimal totalCharge = BigDecimal.valueOf(waterUsage * 2.5); // 简化计算
        BigDecimal baseCharge = BigDecimal.valueOf(waterUsage * 2.0);
        BigDecimal adjustmentAmount = BigDecimal.valueOf(waterUsage * 0.5);
        String calculationDetails = String.format("用水量: %.2f, 单价: 2.5", waterUsage);

        SmartCalculationCacheService.BillCalculationResult result =
                new SmartCalculationCacheService.BillCalculationResult(
                        totalCharge, baseCharge, adjustmentAmount, calculationDetails);

        // 缓存计算结果
        calculationCacheService.cacheBillCalculation(calcKey, result);

        long calculationTime = System.currentTimeMillis() - startTime;
        calculationCacheService.recordCalculationTime("bill_calculation", calculationTime);

        log.debug("账单计算完成并缓存: {}, 耗时: {}ms", calcKey, calculationTime);
        return result;
    }

    /**
     * 批量智能账单计算
     */
    public Map<String, SmartCalculationCacheService.BillCalculationResult> batchSmartCalculateBills(
            List<Map<String, Object>> billCalculationParams) {

        return parallelProcessingEngine.processInParallel(
                "batch_bill_calculation",
                billCalculationParams,
                params -> {
                    Long customerId = (Long) params.get("customerId");
                    Long meterId = (Long) params.get("meterId");
                    Double waterUsage = (Double) params.get("waterUsage");
                    Long priceConfigId = (Long) params.get("priceConfigId");
                    String billingPeriod = (String) params.get("billingPeriod");

                    String key = String.format("%d_%d_%.2f", customerId, meterId, waterUsage);
                    SmartCalculationCacheService.BillCalculationResult result =
                            smartCalculateBill(customerId, meterId, waterUsage, priceConfigId, billingPeriod);

                    return Map.entry(key, result);
                }
        ).getSuccessResults().stream()
         .collect(Collectors.toMap(
                 entry -> ((Map.Entry<String, SmartCalculationCacheService.BillCalculationResult>) entry).getKey(),
                 entry -> ((Map.Entry<String, SmartCalculationCacheService.BillCalculationResult>) entry).getValue()
         ));
    }

    /**
     * 获取批量操作性能统计（增强版）
     */
    public Map<String, Object> getBatchOperationStats() {
        Map<String, Object> stats = new HashMap<>();

        // 基础缓存统计
        stats.put("basicCacheStats", cacheService.getCacheStats());

        // 高级缓存统计
        stats.put("advancedCacheStats", advancedCacheManager.getCacheStatus());

        // 计算缓存统计
        stats.put("calculationCacheStats", calculationCacheService.getCacheEfficiencyReport());

        // 并行处理统计
        stats.put("parallelProcessingStats", parallelProcessingEngine.getProcessingStatistics());

        // 线程池状态
        stats.put("threadPoolStatus", parallelProcessingEngine.getThreadPoolStatus());

        stats.put("timestamp", new Date());

        return stats;
    }

    /**
     * 预热批量处理缓存
     */
    public void preWarmBatchProcessingCache() {
        log.info("开始预热批量处理缓存...");

        // 预热常用的账单查询
        // 这里可以根据实际业务需求预热常用数据

        log.info("批量处理缓存预热完成");
    }
}
