package org.dromara.waterfee.priceManage.service.impl;

import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.waterfee.common.cache.BillPerformanceCacheService;
import org.dromara.waterfee.community.domain.Community;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.dromara.waterfee.priceManage.domain.bo.WaterfeePriceConfigBo;
import org.dromara.waterfee.priceManage.domain.vo.WaterfeePriceConfigVo;
import org.dromara.waterfee.priceManage.domain.WaterfeePriceConfig;
import org.dromara.waterfee.priceManage.domain.WaterfeePriceTier;
import org.dromara.waterfee.priceManage.mapper.WaterfeePriceConfigMapper;
import org.dromara.waterfee.priceManage.mapper.WaterfeePriceTierMapper;
import org.dromara.waterfee.priceManage.service.IWaterfeePriceConfigService;
import org.dromara.common.satoken.utils.LoginHelper;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 阶梯价格配置Service业务层处理
 * 优化版本：添加缓存支持，提升查询性能
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WaterfeePriceConfigServiceImpl implements IWaterfeePriceConfigService {

    private final WaterfeePriceConfigMapper baseMapper;
    private final WaterfeePriceTierMapper tierMapper;
    private final BillPerformanceCacheService cacheService;

    /**
     * 查询阶梯价格配置（优化版本：添加缓存支持）
     *
     * @param id 主键
     * @return 阶梯价格配置
     */
    @Override
    public WaterfeePriceConfigVo queryById(Long id) {
        // 先尝试从缓存获取
        WaterfeePriceConfigVo cachedConfig = cacheService.getCachedPriceConfig(id);
        if (cachedConfig != null) {
            log.debug("从缓存获取价格配置，ID: {}", id);
            return cachedConfig;
        }

        // 缓存未命中，从数据库查询
        WaterfeePriceConfig config = baseMapper.selectById(id);
        WaterfeePriceConfigVo configVo = MapstructUtils.convert(config, WaterfeePriceConfigVo.class);
        if (configVo != null) {
            // 查询关联的阶梯价格（优化：使用缓存）
            List<WaterfeePriceTier> priceTiers = getCachedPriceTiers(id);
            configVo.setPriceTiers(priceTiers);

            // 缓存查询结果
            cacheService.cachePriceConfig(id, configVo);
            log.debug("缓存价格配置，ID: {}", id);
        }
        return configVo;
    }

    /**
     * 获取缓存的阶梯价格信息
     */
    private List<WaterfeePriceTier> getCachedPriceTiers(Long priceConfigId) {
        // 先尝试从缓存获取
        List<WaterfeePriceTier> cachedTiers = cacheService.getCachedPriceTiers(priceConfigId);
        if (cachedTiers != null) {
            return cachedTiers;
        }

        // 缓存未命中，从数据库查询
        LambdaQueryWrapper<WaterfeePriceTier> tierQuery = Wrappers.lambdaQuery();
        tierQuery.eq(WaterfeePriceTier::getPriceConfigId, priceConfigId)
                .orderByAsc(WaterfeePriceTier::getTierNumber);
        List<WaterfeePriceTier> priceTiers = tierMapper.selectList(tierQuery);

        // 缓存查询结果
        if (!priceTiers.isEmpty()) {
            cacheService.cachePriceTiers(priceConfigId, priceTiers);
        }

        return priceTiers;
    }

    /**
     * 分页查询阶梯价格配置列表（优化版本：减少N+1查询问题）
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 阶梯价格配置分页列表
     */
    @Override
    public TableDataInfo<WaterfeePriceConfigVo> queryPageList(WaterfeePriceConfigBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WaterfeePriceConfig> lqw = buildQueryWrapper(bo);
        Page<WaterfeePriceConfigVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);

        // 优化：批量查询阶梯价格数据，避免N+1查询问题
        if (!result.getRecords().isEmpty()) {
            List<Long> configIds = result.getRecords().stream()
                    .map(WaterfeePriceConfigVo::getId)
                    .toList();

            // 批量查询所有相关的阶梯价格
            LambdaQueryWrapper<WaterfeePriceTier> tierQuery = Wrappers.lambdaQuery();
            tierQuery.in(WaterfeePriceTier::getPriceConfigId, configIds)
                    .orderByAsc(WaterfeePriceTier::getPriceConfigId)
                    .orderByAsc(WaterfeePriceTier::getTierNumber);
            List<WaterfeePriceTier> allTiers = tierMapper.selectList(tierQuery);

            // 按配置ID分组
            Map<Long, List<WaterfeePriceTier>> tierMap = allTiers.stream()
                    .collect(java.util.stream.Collectors.groupingBy(WaterfeePriceTier::getPriceConfigId));

            // 设置关联的阶梯价格数据
            result.getRecords().forEach(configVo -> {
                List<WaterfeePriceTier> tiers = tierMap.getOrDefault(configVo.getId(), List.of());
                configVo.setPriceTiers(tiers);

                // 缓存查询结果
                if (!tiers.isEmpty()) {
                    cacheService.cachePriceTiers(configVo.getId(), tiers);
                }
            });
        }

        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的阶梯价格配置列表
     *
     * @param bo 查询条件
     * @return 阶梯价格配置列表
     */
    @Override
    public List<WaterfeePriceConfigVo> queryList(WaterfeePriceConfigBo bo) {
        LambdaQueryWrapper<WaterfeePriceConfig> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WaterfeePriceConfig> buildQueryWrapper(WaterfeePriceConfigBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WaterfeePriceConfig> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(WaterfeePriceConfig::getCreateTime);
        lqw.like(StringUtils.isNotBlank(bo.getName()), WaterfeePriceConfig::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getWaterUseType()), WaterfeePriceConfig::getWaterUseType, bo.getWaterUseType());
        lqw.eq(StringUtils.isNotBlank(bo.getCalculationMethod()), WaterfeePriceConfig::getCalculationMethod, bo.getCalculationMethod());
        lqw.eq(bo.getIsPopulation() != null, WaterfeePriceConfig::getIsPopulation, bo.getIsPopulation());
        lqw.eq(bo.getPopulationCount() != null, WaterfeePriceConfig::getPopulationCount, bo.getPopulationCount());
        lqw.eq(StringUtils.isNotBlank(bo.getDescription()), WaterfeePriceConfig::getDescription, bo.getDescription());
        return lqw;
    }

    /**
     * 新增阶梯价格配置（优化版本：添加事务和缓存管理）
     *
     * @param bo 阶梯价格配置
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(WaterfeePriceConfigBo bo) {
        WaterfeePriceConfig add = MapstructUtils.convert(bo, WaterfeePriceConfig.class);
        validEntityBeforeSave(add);
        add.setCreateBy(LoginHelper.getUserId());
        add.setCreateTime(DateUtils.getNowDate());
        add.setCreateDept(LoginHelper.getDeptId());

        boolean flag = baseMapper.insert(add) > 0;
        if (flag && bo.getPriceTiers() != null && !bo.getPriceTiers().isEmpty()) {
            Long configId = add.getId();

            // 批量插入阶梯价格（优化性能）
            for (WaterfeePriceTier waterfeePriceTier : bo.getPriceTiers()) {
                waterfeePriceTier.setPriceConfigId(configId);
                waterfeePriceTier.setCreateBy(LoginHelper.getUserId());
                waterfeePriceTier.setCreateTime(DateUtils.getNowDate());
                tierMapper.insert(waterfeePriceTier);
            }

            // 缓存新增的阶梯价格数据
            cacheService.cachePriceTiers(configId, bo.getPriceTiers());
            log.info("新增价格配置成功并缓存，ID: {}", configId);
        }
        return flag;
    }

    /**
     * 修改阶梯价格配置（优化版本：添加事务和缓存管理）
     *
     * @param bo 阶梯价格配置
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(WaterfeePriceConfigBo bo) {
        WaterfeePriceConfig update = MapstructUtils.convert(bo, WaterfeePriceConfig.class);
        validEntityBeforeSave(update);
        update.setUpdateBy(LoginHelper.getUserId());
        update.setUpdateTime(DateUtils.getNowDate());

        Long configId = bo.getId();

        // 先清除相关缓存
        cacheService.evictPriceConfigCache(configId);

        // 删除原有阶梯价格
        LambdaQueryWrapper<WaterfeePriceTier> tierQuery = Wrappers.lambdaQuery();
        tierQuery.eq(WaterfeePriceTier::getPriceConfigId, configId);
        tierMapper.delete(tierQuery);

        // 插入新的阶梯价格
        if (bo.getPriceTiers() != null && !bo.getPriceTiers().isEmpty()) {
            for (WaterfeePriceTier waterfeePriceTier : bo.getPriceTiers()) {
                waterfeePriceTier.setPriceConfigId(configId);
                waterfeePriceTier.setUpdateBy(LoginHelper.getUserId());
                waterfeePriceTier.setUpdateTime(DateUtils.getNowDate());
                tierMapper.insert(waterfeePriceTier);
            }

            // 缓存更新后的阶梯价格数据
            cacheService.cachePriceTiers(configId, bo.getPriceTiers());
        }

        boolean result = baseMapper.updateById(update) > 0;
        if (result) {
            log.info("更新价格配置成功并清除缓存，ID: {}", configId);
        }
        return result;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WaterfeePriceConfig entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除阶梯价格配置信息（优化版本：添加缓存清理）
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }

        // 清除相关缓存
        for (Long id : ids) {
            cacheService.evictPriceConfigCache(id);
        }

        // 删除关联的阶梯价格数据
        LambdaQueryWrapper<WaterfeePriceTier> tierQuery = Wrappers.lambdaQuery();
        tierQuery.in(WaterfeePriceTier::getPriceConfigId, ids);
        tierMapper.delete(tierQuery);

        boolean result = baseMapper.deleteByIds(ids) > 0;
        if (result) {
            log.info("批量删除价格配置成功并清除缓存，数量: {}", ids.size());
        }
        return result;
    }

    /**
     * 获取价格配置下拉选择框数据
     *
     * @return ID和名称的Map集合，key为id，value为name
     */
    public Map<String, String> getSelectPriceConfigMap() {
        // 查询所有可用的阶梯价格
        LambdaQueryWrapper<WaterfeePriceConfig> lqw = Wrappers.lambdaQuery();
        // 如果有状态字段，可以添加状态过滤条件
        lqw.orderByAsc(WaterfeePriceConfig::getId); // 按照ID排序

        List<WaterfeePriceConfig> waterfeePriceConfigList = baseMapper.selectList(lqw);

        // 转换为Map<String, String>格式，key为社区ID，value为社区名称
        Map<String, String> waterfeePriceConfigMap = new HashMap<>(waterfeePriceConfigList.size());
        for (WaterfeePriceConfig waterfPriceConfig : waterfeePriceConfigList) {
            waterfeePriceConfigMap.put(waterfPriceConfig.getId().toString(), waterfPriceConfig.getName());
        }

        return waterfeePriceConfigMap;
    }
}
