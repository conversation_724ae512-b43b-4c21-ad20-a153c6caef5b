package org.dromara.waterfee.common.cache;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.security.MessageDigest;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 智能计算结果缓存服务
 * 专门用于缓存复杂的计费计算结果，避免重复计算
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SmartCalculationCacheService {

    private final AdvancedCacheManager cacheManager;
    
    // 缓存配置
    private static final String CALCULATION_PREFIX = "calc:";
    private static final String TIER_CALC_PREFIX = "tier_calc:";
    private static final String BILL_CALC_PREFIX = "bill_calc:";
    private static final String USER_CALC_PREFIX = "user_calc:";
    
    private static final Duration CALCULATION_TTL = Duration.ofMinutes(30);
    private static final Duration TIER_CALC_TTL = Duration.ofHours(2);
    private static final Duration BILL_CALC_TTL = Duration.ofMinutes(15);
    
    // 计算结果统计
    private final Map<String, CalculationStats> calculationStats = new ConcurrentHashMap<>();
    
    /**
     * 计算统计信息
     */
    public static class CalculationStats {
        private long cacheHits = 0;
        private long cacheMisses = 0;
        private long calculationTime = 0;
        private long savedTime = 0;
        
        public void recordCacheHit(long savedTimeMs) {
            cacheHits++;
            savedTime += savedTimeMs;
        }
        
        public void recordCacheMiss(long calculationTimeMs) {
            cacheMisses++;
            calculationTime += calculationTimeMs;
        }
        
        public double getCacheHitRate() {
            long total = cacheHits + cacheMisses;
            return total > 0 ? (double) cacheHits / total : 0.0;
        }
        
        // Getters
        public long getCacheHits() { return cacheHits; }
        public long getCacheMisses() { return cacheMisses; }
        public long getCalculationTime() { return calculationTime; }
        public long getSavedTime() { return savedTime; }
    }
    
    /**
     * 阶梯计费计算结果
     */
    public static class TierCalculationResult {
        private final BigDecimal totalAmount;
        private final Map<Integer, BigDecimal> tierUsages;
        private final Map<Integer, BigDecimal> tierAmounts;
        private final long calculationTime;
        
        public TierCalculationResult(BigDecimal totalAmount, 
                                   Map<Integer, BigDecimal> tierUsages,
                                   Map<Integer, BigDecimal> tierAmounts) {
            this.totalAmount = totalAmount;
            this.tierUsages = new HashMap<>(tierUsages);
            this.tierAmounts = new HashMap<>(tierAmounts);
            this.calculationTime = System.currentTimeMillis();
        }
        
        // Getters
        public BigDecimal getTotalAmount() { return totalAmount; }
        public Map<Integer, BigDecimal> getTierUsages() { return new HashMap<>(tierUsages); }
        public Map<Integer, BigDecimal> getTierAmounts() { return new HashMap<>(tierAmounts); }
        public long getCalculationTime() { return calculationTime; }
    }
    
    /**
     * 账单计算结果
     */
    public static class BillCalculationResult {
        private final BigDecimal totalCharge;
        private final BigDecimal baseCharge;
        private final BigDecimal adjustmentAmount;
        private final String calculationDetails;
        private final long calculationTime;
        
        public BillCalculationResult(BigDecimal totalCharge, BigDecimal baseCharge, 
                                   BigDecimal adjustmentAmount, String calculationDetails) {
            this.totalCharge = totalCharge;
            this.baseCharge = baseCharge;
            this.adjustmentAmount = adjustmentAmount;
            this.calculationDetails = calculationDetails;
            this.calculationTime = System.currentTimeMillis();
        }
        
        // Getters
        public BigDecimal getTotalCharge() { return totalCharge; }
        public BigDecimal getBaseCharge() { return baseCharge; }
        public BigDecimal getAdjustmentAmount() { return adjustmentAmount; }
        public String getCalculationDetails() { return calculationDetails; }
        public long getCalculationTime() { return calculationTime; }
    }
    
    /**
     * 缓存阶梯计费计算结果
     */
    public void cacheTierCalculation(Long priceConfigId, Double waterUsage, Double ladderUsage, 
                                   Integer populationCount, TierCalculationResult result) {
        String key = generateTierCalcKey(priceConfigId, waterUsage, ladderUsage, populationCount);
        cacheManager.put(TIER_CALC_PREFIX + key, result, TIER_CALC_TTL);
        log.debug("缓存阶梯计费结果: {}", key);
    }
    
    /**
     * 获取缓存的阶梯计费计算结果
     */
    public TierCalculationResult getCachedTierCalculation(Long priceConfigId, Double waterUsage, 
                                                        Double ladderUsage, Integer populationCount) {
        String key = generateTierCalcKey(priceConfigId, waterUsage, ladderUsage, populationCount);
        String calcType = "tier_calculation";
        
        long startTime = System.currentTimeMillis();
        TierCalculationResult result = cacheManager.get(TIER_CALC_PREFIX + key, TierCalculationResult.class);
        
        CalculationStats stats = calculationStats.computeIfAbsent(calcType, k -> new CalculationStats());
        
        if (result != null) {
            long savedTime = System.currentTimeMillis() - startTime;
            stats.recordCacheHit(savedTime);
            log.debug("阶梯计费缓存命中: {}", key);
        } else {
            stats.recordCacheMiss(0);
            log.debug("阶梯计费缓存未命中: {}", key);
        }
        
        return result;
    }
    
    /**
     * 缓存账单计算结果
     */
    public void cacheBillCalculation(String billKey, BillCalculationResult result) {
        cacheManager.put(BILL_CALC_PREFIX + billKey, result, BILL_CALC_TTL);
        log.debug("缓存账单计算结果: {}", billKey);
    }
    
    /**
     * 获取缓存的账单计算结果
     */
    public BillCalculationResult getCachedBillCalculation(String billKey) {
        String calcType = "bill_calculation";
        
        long startTime = System.currentTimeMillis();
        BillCalculationResult result = cacheManager.get(BILL_CALC_PREFIX + billKey, BillCalculationResult.class);
        
        CalculationStats stats = calculationStats.computeIfAbsent(calcType, k -> new CalculationStats());
        
        if (result != null) {
            long savedTime = System.currentTimeMillis() - startTime;
            stats.recordCacheHit(savedTime);
            log.debug("账单计算缓存命中: {}", billKey);
        } else {
            stats.recordCacheMiss(0);
            log.debug("账单计算缓存未命中: {}", billKey);
        }
        
        return result;
    }
    
    /**
     * 批量缓存阶梯计费结果
     */
    public void batchCacheTierCalculations(Map<String, TierCalculationResult> calculations) {
        Map<String, TierCalculationResult> cacheData = new HashMap<>();
        
        for (Map.Entry<String, TierCalculationResult> entry : calculations.entrySet()) {
            cacheData.put(TIER_CALC_PREFIX + entry.getKey(), entry.getValue());
        }
        
        cacheManager.warmUp("tier_calculations", cacheData, TIER_CALC_TTL);
        log.info("批量缓存阶梯计费结果，数量: {}", calculations.size());
    }
    
    /**
     * 批量获取阶梯计费缓存结果
     */
    public Map<String, TierCalculationResult> batchGetTierCalculations(Collection<String> keys) {
        List<String> prefixedKeys = keys.stream()
                .map(key -> TIER_CALC_PREFIX + key)
                .toList();
        
        Map<String, TierCalculationResult> results = cacheManager.batchGet(prefixedKeys, TierCalculationResult.class);
        
        // 移除前缀
        Map<String, TierCalculationResult> finalResults = new HashMap<>();
        for (Map.Entry<String, TierCalculationResult> entry : results.entrySet()) {
            String originalKey = entry.getKey().substring(TIER_CALC_PREFIX.length());
            finalResults.put(originalKey, entry.getValue());
        }
        
        log.debug("批量获取阶梯计费缓存，请求: {}, 命中: {}", keys.size(), finalResults.size());
        return finalResults;
    }
    
    /**
     * 智能预热计算缓存
     */
    public void preWarmCalculationCache(Long priceConfigId, List<Double> commonUsages, 
                                      List<Double> commonLadderUsages, List<Integer> populationCounts) {
        log.info("开始预热计算缓存，价格配置: {}", priceConfigId);
        
        Map<String, Object> preWarmData = new HashMap<>();
        int count = 0;
        
        for (Double usage : commonUsages) {
            for (Double ladderUsage : commonLadderUsages) {
                for (Integer population : populationCounts) {
                    String key = generateTierCalcKey(priceConfigId, usage, ladderUsage, population);
                    // 这里可以预先计算一些常用的结果
                    // 实际实现中，可以调用真实的计算逻辑
                    preWarmData.put(TIER_CALC_PREFIX + key, null); // 占位符
                    count++;
                }
            }
        }
        
        log.info("计算缓存预热完成，生成缓存键: {}", count);
    }
    
    /**
     * 清除用户相关的计算缓存
     */
    public void evictUserCalculationCache(Long userId) {
        // 这里可以实现更精确的缓存清理逻辑
        String pattern = USER_CALC_PREFIX + userId + ":*";
        log.info("清除用户计算缓存: {}", userId);
    }
    
    /**
     * 清除价格配置相关的计算缓存
     */
    public void evictPriceConfigCalculationCache(Long priceConfigId) {
        // 清除与特定价格配置相关的所有计算缓存
        log.info("清除价格配置计算缓存: {}", priceConfigId);
    }
    
    /**
     * 生成阶梯计费缓存键
     */
    private String generateTierCalcKey(Long priceConfigId, Double waterUsage, 
                                     Double ladderUsage, Integer populationCount) {
        return String.format("%d_%.2f_%.2f_%d", 
                priceConfigId, waterUsage, ladderUsage, populationCount != null ? populationCount : 0);
    }
    
    /**
     * 生成账单计算缓存键
     */
    public String generateBillCalcKey(Long customerId, Long meterId, Double waterUsage, 
                                    Long priceConfigId, String billingPeriod) {
        try {
            String input = String.format("%d_%d_%.2f_%d_%s", 
                    customerId, meterId, waterUsage, priceConfigId, billingPeriod);
            
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hash = md.digest(input.getBytes());
            StringBuilder sb = new StringBuilder();
            for (byte b : hash) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (Exception e) {
            log.warn("生成账单计算缓存键失败，使用简单键", e);
            return String.format("%d_%d_%.2f", customerId, meterId, waterUsage);
        }
    }
    
    /**
     * 记录计算时间统计
     */
    public void recordCalculationTime(String calculationType, long calculationTimeMs) {
        CalculationStats stats = calculationStats.computeIfAbsent(calculationType, k -> new CalculationStats());
        stats.recordCacheMiss(calculationTimeMs);
    }
    
    /**
     * 获取计算统计信息
     */
    public Map<String, CalculationStats> getCalculationStatistics() {
        return new HashMap<>(calculationStats);
    }
    
    /**
     * 获取缓存效率报告
     */
    public Map<String, Object> getCacheEfficiencyReport() {
        Map<String, Object> report = new HashMap<>();
        
        long totalHits = calculationStats.values().stream().mapToLong(CalculationStats::getCacheHits).sum();
        long totalMisses = calculationStats.values().stream().mapToLong(CalculationStats::getCacheMisses).sum();
        long totalSavedTime = calculationStats.values().stream().mapToLong(CalculationStats::getSavedTime).sum();
        long totalCalculationTime = calculationStats.values().stream().mapToLong(CalculationStats::getCalculationTime).sum();
        
        double overallHitRate = (totalHits + totalMisses) > 0 ? 
                (double) totalHits / (totalHits + totalMisses) : 0.0;
        
        report.put("overallHitRate", overallHitRate);
        report.put("totalCacheHits", totalHits);
        report.put("totalCacheMisses", totalMisses);
        report.put("totalTimeSaved", totalSavedTime);
        report.put("totalCalculationTime", totalCalculationTime);
        report.put("calculationTypes", calculationStats.keySet());
        
        // 计算性能提升百分比
        if (totalCalculationTime > 0) {
            double performanceImprovement = (double) totalSavedTime / (totalCalculationTime + totalSavedTime) * 100;
            report.put("performanceImprovement", performanceImprovement);
        }
        
        return report;
    }
}
