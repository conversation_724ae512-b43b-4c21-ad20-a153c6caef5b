package org.dromara.waterfee.bill.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.waterfee.bill.domain.WaterfeeBill;
import org.dromara.waterfee.bill.domain.vo.MeterBookBillSummaryVo;
import org.dromara.waterfee.bill.domain.vo.WaterfeeBillVo;
import org.dromara.waterfee.statisticalReport.domain.MechanicalWatchArrearsDetailsVO;

import java.io.Serializable;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 账单管理Mapper接口
 *
 * <AUTHOR>
 * @date 2023-10-31
 */
public interface WaterfeeBillMapper extends BaseMapperPlus<WaterfeeBill, WaterfeeBillVo> {

    /**
     * 根据ID查询账单详情，包含用户信息
     *
     * @param billId 账单ID
     * @return 账单详情VO，包含用户信息
     */
    WaterfeeBillVo selectVoById(@Param("billId") Serializable billId);

    List<WaterfeeBillVo> selectVoListByUserId(@Param("userId") Long userId, @Param("status") String status, @Param("month") String month);

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    default <P extends IPage<WaterfeeBillVo>> P selectVoPage(IPage<WaterfeeBill> page, Wrapper<WaterfeeBill> wrapper) {
        return selectVoPage(page, wrapper, this.currentVoClass());
    }

    /**
     * 查询账单列表，包含用户信息
     *
     * @param page    分页参数
     * @param wrapper 查询条件
     * @return 账单列表
     */
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    <P extends IPage<WaterfeeBillVo>> P selectBillVoPage(@Param("page") IPage<WaterfeeBill> page, @Param("ew") Wrapper<WaterfeeBill> wrapper);

    /**
     * 查询账单列表，包含用户信息
     *
     * @param page    分页参数
     * @param wrapper 查询条件
     * @return 账单列表
     */
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    <P extends IPage<WaterfeeBillVo>> P selectBillVoPageDetail(@Param("page") IPage<WaterfeeBill> page, @Param("ew") Wrapper<WaterfeeBill> wrapper);

    /**
     * 查询按表册分组的账单汇总信息
     *
     * @param page    分页参数
     * @param wrapper 查询条件
     * @return 表册账单汇总信息分页列表
     */
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    Page<MeterBookBillSummaryVo> selectMeterBookBillSummaryPage(@Param("page") IPage<?> page, @Param("ew") Wrapper<?> wrapper);

    /**
     * 机械表欠费明细表（搜索条件是区册）
     */
    List<MechanicalWatchArrearsDetailsVO> getMechanicalWatchArrearsDetails(@Param("meterBookId") Long meterBookId);

    // =====================================================
    // 批量操作方法（性能优化）
    // =====================================================

    /**
     * 批量更新账单状态
     *
     * @param billIds 账单ID集合
     * @param status 新状态
     * @param updateBy 更新人
     * @param updateTime 更新时间
     * @return 更新数量
     */
    int batchUpdateBillStatus(@Param("billIds") Collection<Long> billIds,
                             @Param("status") String status,
                             @Param("updateBy") Long updateBy,
                             @Param("updateTime") Date updateTime);

    /**
     * 批量查询账单信息
     *
     * @param billIds 账单ID集合
     * @return 账单列表
     */
    List<WaterfeeBill> batchSelectBillsByIds(@Param("billIds") Collection<Long> billIds);

    /**
     * 批量获取账单详细信息（包含客户信息）
     *
     * @param billIds 账单ID集合
     * @return 账单详细信息列表
     */
    List<Map<String, Object>> batchGetBillDetailsWithCustomer(@Param("billIds") Collection<Long> billIds);

    /**
     * 批量获取账单详细信息（包含客户信息）- 返回Map结构
     *
     * @param billIds 账单ID集合
     * @return 以bill_id为键的账单详细信息Map
     */
    @MapKey("bill_id")
    Map<String, Map<String, Object>> batchGetBillDetailsWithCustomerAsMap(@Param("billIds") Collection<Long> billIds);

    /**
     * 批量发行账单（按表册ID）
     *
     * @param meterBookIds 表册ID集合
     * @param issueDate 发行日期
     * @return 更新数量
     */
    int batchIssueBillsByMeterBookIds(@Param("meterBookIds") Collection<Long> meterBookIds,
                                     @Param("issueDate") Date issueDate);

    /**
     * 批量计算账单统计信息
     *
     * @param customerIds 客户ID集合
     * @return 统计信息列表
     */
    List<Map<String, Object>> batchCalculateBillStatistics(@Param("customerIds") Collection<Long> customerIds);

    /**
     * 批量计算账单统计信息 - 返回Map结构
     *
     * @param customerIds 客户ID集合
     * @return 以customer_id为键的统计信息Map
     */
    @MapKey("customer_id")
    Map<String, Map<String, Object>> batchCalculateBillStatisticsAsMap(@Param("customerIds") Collection<Long> customerIds);

    /**
     * 统计无效账单数量（用于删除前校验）
     *
     * @param billIds 账单ID集合
     * @return 无效账单数量
     */
    int countInvalidBillsForDeletion(@Param("billIds") Collection<Long> billIds);

    /**
     * 批量逻辑删除账单
     *
     * @param billIds 账单ID集合
     * @param deleteTime 删除时间
     * @return 删除数量
     */
    int batchLogicalDeleteBills(@Param("billIds") Collection<Long> billIds,
                               @Param("deleteTime") Date deleteTime);
}
