package org.dromara.waterfee.common.monitor;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.waterfee.common.cache.AdvancedCacheManager;
import org.dromara.waterfee.common.cache.SmartCalculationCacheService;
import org.dromara.waterfee.common.parallel.ParallelProcessingEngine;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 性能监控服务
 * 提供系统性能监控、性能指标收集、性能报告生成等功能
 *
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PerformanceMonitorService {

    private final AdvancedCacheManager cacheManager;
    private final SmartCalculationCacheService calculationCacheService;
    private final ParallelProcessingEngine parallelProcessingEngine;

    @Value("${performance.monitoring.enabled:true}")
    private boolean monitoringEnabled;

    @Value("${performance.monitoring.collect-interval:60}")
    private int collectIntervalSeconds;

    @Value("${performance.monitoring.report-interval:300}")
    private int reportIntervalSeconds;

    @Value("${performance.monitoring.history-retention-days:30}")
    private int historyRetentionDays;

    private ScheduledExecutorService scheduler;
    private final Map<String, List<PerformanceMetric>> metricsHistory = new ConcurrentHashMap<>();
    private final Map<String, PerformanceAlert> activeAlerts = new ConcurrentHashMap<>();

    // JVM监控
    private final MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
    private final com.sun.management.OperatingSystemMXBean osBean = (com.sun.management.OperatingSystemMXBean) ManagementFactory.getOperatingSystemMXBean();


    /**
     * 性能指标
     */
    public static class PerformanceMetric {
        private final String name;
        private final double value;
        private final String unit;
        private final long timestamp;
        private final Map<String, Object> metadata;

        public PerformanceMetric(String name, double value, String unit) {
            this.name = name;
            this.value = value;
            this.unit = unit;
            this.timestamp = System.currentTimeMillis();
            this.metadata = new HashMap<>();
        }

        public PerformanceMetric(String name, double value, String unit, Map<String, Object> metadata) {
            this.name = name;
            this.value = value;
            this.unit = unit;
            this.timestamp = System.currentTimeMillis();
            this.metadata = new HashMap<>(metadata);
        }

        // Getters
        public String getName() {
            return name;
        }

        public double getValue() {
            return value;
        }

        public String getUnit() {
            return unit;
        }

        public long getTimestamp() {
            return timestamp;
        }

        public Map<String, Object> getMetadata() {
            return new HashMap<>(metadata);
        }
    }

    /**
     * 性能告警
     */
    public static class PerformanceAlert {
        private final String alertType;
        private final String message;
        private final double threshold;
        private final double currentValue;
        private final long timestamp;
        private final String severity;

        public PerformanceAlert(String alertType, String message, double threshold,
                                double currentValue, String severity) {
            this.alertType = alertType;
            this.message = message;
            this.threshold = threshold;
            this.currentValue = currentValue;
            this.timestamp = System.currentTimeMillis();
            this.severity = severity;
        }

        // Getters
        public String getAlertType() {
            return alertType;
        }

        public String getMessage() {
            return message;
        }

        public double getThreshold() {
            return threshold;
        }

        public double getCurrentValue() {
            return currentValue;
        }

        public long getTimestamp() {
            return timestamp;
        }

        public String getSeverity() {
            return severity;
        }
    }

    @PostConstruct
    public void init() {
        if (!monitoringEnabled) {
            log.info("性能监控已禁用");
            return;
        }

        scheduler = Executors.newScheduledThreadPool(2, r -> {
            Thread thread = new Thread(r, "performance-monitor");
            thread.setDaemon(true);
            return thread;
        });

        // 定期收集性能指标
        scheduler.scheduleAtFixedRate(this::collectMetrics,
                collectIntervalSeconds, collectIntervalSeconds, TimeUnit.SECONDS);

        // 定期生成性能报告
        scheduler.scheduleAtFixedRate(this::generatePerformanceReport,
                reportIntervalSeconds, reportIntervalSeconds, TimeUnit.SECONDS);

        // 定期清理历史数据
        scheduler.scheduleAtFixedRate(this::cleanupHistoryData,
                24, 24, TimeUnit.HOURS);

        log.info("性能监控服务已启动，收集间隔: {}s, 报告间隔: {}s",
                collectIntervalSeconds, reportIntervalSeconds);
    }

    @PreDestroy
    public void destroy() {
        if (scheduler != null) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(10, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        log.info("性能监控服务已关闭");
    }

    /**
     * 收集性能指标
     */
    private void collectMetrics() {
        try {
            // JVM内存指标
            collectMemoryMetrics();

            // 系统CPU指标
            collectCpuMetrics();

            // 缓存性能指标
            collectCacheMetrics();

            // 并行处理指标
            collectParallelProcessingMetrics();

            // 计算缓存指标
            collectCalculationCacheMetrics();

            // 检查性能告警
            checkPerformanceAlerts();

        } catch (Exception e) {
            log.error("收集性能指标失败", e);
        }
    }

    /**
     * 收集内存指标
     */
    private void collectMemoryMetrics() {
        long heapUsed = memoryBean.getHeapMemoryUsage().getUsed();
        long heapMax = memoryBean.getHeapMemoryUsage().getMax();
        long nonHeapUsed = memoryBean.getNonHeapMemoryUsage().getUsed();

        double heapUsagePercent = heapMax > 0 ? (double) heapUsed / heapMax * 100 : 0;

        recordMetric("jvm.memory.heap.used", heapUsed / 1024.0 / 1024.0, "MB");
        recordMetric("jvm.memory.heap.max", heapMax / 1024.0 / 1024.0, "MB");
        recordMetric("jvm.memory.heap.usage", heapUsagePercent, "%");
        recordMetric("jvm.memory.nonheap.used", nonHeapUsed / 1024.0 / 1024.0, "MB");
    }

    /**
     * 收集CPU指标
     */
    private void collectCpuMetrics() {
        double cpuUsage = osBean.getProcessCpuLoad() * 100;
        if (cpuUsage >= 0) {
            recordMetric("system.cpu.usage", cpuUsage, "%");
        }

        int availableProcessors = osBean.getAvailableProcessors();
        recordMetric("system.cpu.processors", availableProcessors, "count");
    }

    /**
     * 收集缓存指标
     */
    private void collectCacheMetrics() {
        Map<String, Object> cacheStatus = cacheManager.getCacheStatus();

        recordMetric("cache.local.size", (Integer) cacheStatus.get("localCacheSize"), "count");
        recordMetric("cache.hit.rate", (Double) cacheStatus.get("overallHitRate") * 100, "%");
        recordMetric("cache.total.hits", (Long) cacheStatus.get("totalHits"), "count");
        recordMetric("cache.total.misses", (Long) cacheStatus.get("totalMisses"), "count");
    }

    /**
     * 收集并行处理指标
     */
    private void collectParallelProcessingMetrics() {
        Map<String, Object> threadPoolStatus = parallelProcessingEngine.getThreadPoolStatus();

        recordMetric("threadpool.active.count", (Integer) threadPoolStatus.get("activeCount"), "count");
        recordMetric("threadpool.pool.size", (Integer) threadPoolStatus.get("poolSize"), "count");
        recordMetric("threadpool.queue.size", (Integer) threadPoolStatus.get("queueSize"), "count");
        recordMetric("threadpool.completed.tasks", (Long) threadPoolStatus.get("completedTaskCount"), "count");
    }

    /**
     * 收集计算缓存指标
     */
    private void collectCalculationCacheMetrics() {
        Map<String, Object> efficiencyReport = calculationCacheService.getCacheEfficiencyReport();

        recordMetric("calc.cache.hit.rate", (Double) efficiencyReport.get("overallHitRate") * 100, "%");
        recordMetric("calc.cache.total.hits", (Long) efficiencyReport.get("totalCacheHits"), "count");
        recordMetric("calc.cache.total.misses", (Long) efficiencyReport.get("totalCacheMisses"), "count");
        recordMetric("calc.cache.time.saved", (Long) efficiencyReport.get("totalTimeSaved"), "ms");

        if (efficiencyReport.containsKey("performanceImprovement")) {
            recordMetric("calc.cache.performance.improvement",
                    (Double) efficiencyReport.get("performanceImprovement"), "%");
        }
    }

    /**
     * 记录性能指标
     */
    private void recordMetric(String name, double value, String unit) {
        PerformanceMetric metric = new PerformanceMetric(name, value, unit);

        metricsHistory.computeIfAbsent(name, k -> new ArrayList<>()).add(metric);

        // 限制历史数据大小
        List<PerformanceMetric> history = metricsHistory.get(name);
        if (history.size() > 1000) { // 保留最近1000个数据点
            history.remove(0);
        }
    }

    /**
     * 检查性能告警
     */
    private void checkPerformanceAlerts() {
        // 内存使用率告警
        checkMemoryAlert();

        // CPU使用率告警
        checkCpuAlert();

        // 缓存命中率告警
        checkCacheHitRateAlert();

        // 线程池队列告警
        checkThreadPoolQueueAlert();
    }

    /**
     * 检查内存告警
     */
    private void checkMemoryAlert() {
        List<PerformanceMetric> heapUsageMetrics = metricsHistory.get("jvm.memory.heap.usage");
        if (heapUsageMetrics != null && !heapUsageMetrics.isEmpty()) {
            PerformanceMetric latest = heapUsageMetrics.get(heapUsageMetrics.size() - 1);

            if (latest.getValue() > 80) {
                PerformanceAlert alert = new PerformanceAlert(
                        "HIGH_MEMORY_USAGE",
                        String.format("JVM堆内存使用率过高: %.2f%%", latest.getValue()),
                        80.0,
                        latest.getValue(),
                        latest.getValue() > 90 ? "CRITICAL" : "WARNING"
                );
                activeAlerts.put("HIGH_MEMORY_USAGE", alert);
            } else {
                activeAlerts.remove("HIGH_MEMORY_USAGE");
            }
        }
    }

    /**
     * 检查CPU告警
     */
    private void checkCpuAlert() {
        List<PerformanceMetric> cpuUsageMetrics = metricsHistory.get("system.cpu.usage");
        if (cpuUsageMetrics != null && !cpuUsageMetrics.isEmpty()) {
            PerformanceMetric latest = cpuUsageMetrics.get(cpuUsageMetrics.size() - 1);

            if (latest.getValue() > 80) {
                PerformanceAlert alert = new PerformanceAlert(
                        "HIGH_CPU_USAGE",
                        String.format("CPU使用率过高: %.2f%%", latest.getValue()),
                        80.0,
                        latest.getValue(),
                        latest.getValue() > 90 ? "CRITICAL" : "WARNING"
                );
                activeAlerts.put("HIGH_CPU_USAGE", alert);
            } else {
                activeAlerts.remove("HIGH_CPU_USAGE");
            }
        }
    }

    /**
     * 检查缓存命中率告警
     */
    private void checkCacheHitRateAlert() {
        List<PerformanceMetric> hitRateMetrics = metricsHistory.get("cache.hit.rate");
        if (hitRateMetrics != null && !hitRateMetrics.isEmpty()) {
            PerformanceMetric latest = hitRateMetrics.get(hitRateMetrics.size() - 1);

            if (latest.getValue() < 60) {
                PerformanceAlert alert = new PerformanceAlert(
                        "LOW_CACHE_HIT_RATE",
                        String.format("缓存命中率过低: %.2f%%", latest.getValue()),
                        60.0,
                        latest.getValue(),
                        latest.getValue() < 40 ? "CRITICAL" : "WARNING"
                );
                activeAlerts.put("LOW_CACHE_HIT_RATE", alert);
            } else {
                activeAlerts.remove("LOW_CACHE_HIT_RATE");
            }
        }
    }

    /**
     * 检查线程池队列告警
     */
    private void checkThreadPoolQueueAlert() {
        List<PerformanceMetric> queueSizeMetrics = metricsHistory.get("threadpool.queue.size");
        if (queueSizeMetrics != null && !queueSizeMetrics.isEmpty()) {
            PerformanceMetric latest = queueSizeMetrics.get(queueSizeMetrics.size() - 1);

            if (latest.getValue() > 150) { // 队列容量的75%
                PerformanceAlert alert = new PerformanceAlert(
                        "HIGH_THREAD_POOL_QUEUE",
                        String.format("线程池队列积压过多: %.0f", latest.getValue()),
                        150.0,
                        latest.getValue(),
                        latest.getValue() > 180 ? "CRITICAL" : "WARNING"
                );
                activeAlerts.put("HIGH_THREAD_POOL_QUEUE", alert);
            } else {
                activeAlerts.remove("HIGH_THREAD_POOL_QUEUE");
            }
        }
    }

    /**
     * 生成性能报告
     */
    private void generatePerformanceReport() {
        if (metricsHistory.isEmpty()) {
            return;
        }

        log.info("=== 性能监控报告 ===");

        // 内存使用情况
        reportMetricSummary("JVM堆内存使用率", "jvm.memory.heap.usage", "%");

        // CPU使用情况
        reportMetricSummary("CPU使用率", "system.cpu.usage", "%");

        // 缓存性能
        reportMetricSummary("缓存命中率", "cache.hit.rate", "%");

        // 线程池状态
        reportMetricSummary("线程池活跃线程数", "threadpool.active.count", "个");
        reportMetricSummary("线程池队列大小", "threadpool.queue.size", "个");

        // 计算缓存性能
        reportMetricSummary("计算缓存命中率", "calc.cache.hit.rate", "%");

        // 活跃告警
        if (!activeAlerts.isEmpty()) {
            log.warn("=== 活跃告警 ===");
            activeAlerts.values().forEach(alert -> {
                log.warn("[{}] {}: {} (阈值: {}, 当前值: {})",
                        alert.getSeverity(), alert.getAlertType(), alert.getMessage(),
                        alert.getThreshold(), alert.getCurrentValue());
            });
        }

        log.info("=== 性能监控报告结束 ===");
    }

    /**
     * 报告指标摘要
     */
    private void reportMetricSummary(String displayName, String metricName, String unit) {
        List<PerformanceMetric> metrics = metricsHistory.get(metricName);
        if (metrics == null || metrics.isEmpty()) {
            return;
        }

        // 计算最近5分钟的统计信息
        long fiveMinutesAgo = System.currentTimeMillis() - 5 * 60 * 1000;
        List<PerformanceMetric> recentMetrics = metrics.stream()
                .filter(m -> m.getTimestamp() > fiveMinutesAgo)
                .toList();

        if (recentMetrics.isEmpty()) {
            return;
        }

        double avg = recentMetrics.stream().mapToDouble(PerformanceMetric::getValue).average().orElse(0);
        double max = recentMetrics.stream().mapToDouble(PerformanceMetric::getValue).max().orElse(0);
        double min = recentMetrics.stream().mapToDouble(PerformanceMetric::getValue).min().orElse(0);
        double current = recentMetrics.get(recentMetrics.size() - 1).getValue();

        log.info("{}: 当前={}{}, 平均={}{}, 最大={}{}, 最小={}{}",
                displayName, current, unit, avg, unit, max, unit, min, unit);
    }

    /**
     * 清理历史数据
     */
    private void cleanupHistoryData() {
        long cutoffTime = System.currentTimeMillis() - historyRetentionDays * 24 * 60 * 60 * 1000L;

        metricsHistory.values().forEach(metrics -> {
            metrics.removeIf(metric -> metric.getTimestamp() < cutoffTime);
        });

        log.info("清理历史性能数据完成，保留天数: {}", historyRetentionDays);
    }

    /**
     * 获取性能指标历史
     */
    public List<PerformanceMetric> getMetricHistory(String metricName) {
        return new ArrayList<>(metricsHistory.getOrDefault(metricName, Collections.emptyList()));
    }

    /**
     * 获取活跃告警
     */
    public Map<String, PerformanceAlert> getActiveAlerts() {
        return new HashMap<>(activeAlerts);
    }

    /**
     * 获取性能摘要
     */
    public Map<String, Object> getPerformanceSummary() {
        Map<String, Object> summary = new HashMap<>();

        // 系统状态
        summary.put("monitoringEnabled", monitoringEnabled);
        summary.put("metricsCount", metricsHistory.size());
        summary.put("activeAlertsCount", activeAlerts.size());

        // 最新指标
        Map<String, Double> latestMetrics = new HashMap<>();
        metricsHistory.forEach((name, metrics) -> {
            if (!metrics.isEmpty()) {
                latestMetrics.put(name, metrics.get(metrics.size() - 1).getValue());
            }
        });
        summary.put("latestMetrics", latestMetrics);

        // 告警信息
        summary.put("activeAlerts", new ArrayList<>(activeAlerts.values()));

        return summary;
    }
}
