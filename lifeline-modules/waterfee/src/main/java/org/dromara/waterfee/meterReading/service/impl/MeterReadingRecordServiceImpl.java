package org.dromara.waterfee.meterReading.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateFormatUtils;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.waterfee.common.cache.BillPerformanceCacheService;
import org.dromara.waterfee.bill.domain.bo.WaterfeeBillBo;
import org.dromara.waterfee.bill.domain.vo.WaterfeeBillVo;
import org.dromara.waterfee.bill.service.IWaterfeeBillService;
import org.dromara.waterfee.meter.domain.vo.WaterfeeMeterVo;
import org.dromara.waterfee.meter.service.IWaterfeeMeterService;
import org.dromara.waterfee.meterReading.domain.WaterfeeMeterReadingRecord;
import org.dromara.waterfee.meterReading.domain.WaterfeeReadingTask;
import org.dromara.waterfee.meterReading.domain.bo.MeterReadingRecordBo;
import org.dromara.waterfee.meterReading.domain.bo.WaterfeeReadingTaskBo;
import org.dromara.waterfee.meterReading.domain.vo.MeterReadingRecordVo;
import org.dromara.waterfee.meterReading.domain.vo.WaterfeeReadingTaskVo;
import org.dromara.waterfee.meterReading.mapper.MeterReadingRecordMapper;
import org.dromara.waterfee.meterReading.service.IMeterReadingCommonService;
import org.dromara.waterfee.meterReading.service.IMeterReadingRecordService;
import org.dromara.waterfee.meterReading.service.IWaterfeeReadingTaskService;
import org.dromara.waterfee.priceManage.domain.WaterfeePriceConfig;
import org.dromara.waterfee.priceManage.domain.WaterfeePriceTier;
import org.dromara.waterfee.priceManage.mapper.WaterfeePriceConfigMapper;
import org.dromara.waterfee.priceManage.mapper.WaterfeePriceTierMapper;
import org.dromara.waterfee.user.domain.WaterfeeUser;
import org.dromara.waterfee.user.service.IWaterfeeUserService;
import org.springframework.beans.BeanUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 抄表记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-24
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class MeterReadingRecordServiceImpl implements IMeterReadingRecordService {

    private final MeterReadingRecordMapper baseMapper;
    private final JdbcTemplate jdbcTemplate;
    private final IMeterReadingCommonService meterReadingCommonService;
    private final IWaterfeeBillService waterfeeBillService;
    private final IWaterfeeMeterService waterfeeMeterService;
    private final IWaterfeeUserService waterfeeUserService;
    private final WaterfeePriceTierMapper waterfeePriceTierMapper;
    private final WaterfeePriceConfigMapper waterfeePriceConfigMapper;
    private final IWaterfeeReadingTaskService waterfeeReadingTaskService;
    private final BillPerformanceCacheService cacheService;

    /**
     * 查询抄表记录
     *
     * @param recordId 主键
     * @return 抄表记录
     */
    @Override
    public MeterReadingRecordVo queryById(Long recordId) {
        return baseMapper.selectVoById(recordId);
    }

    /**
     * 查询抄表记录列表
     *
     * @param record 抄表记录
     * @return 抄表记录
     */
    @Override
    public TableDataInfo<MeterReadingRecordVo> queryPageList(WaterfeeMeterReadingRecord record, PageQuery pageQuery) {
        // 构建查询条件
        com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<WaterfeeMeterReadingRecord> lqw = this
            .buildQueryWrapper(record);
        // 添加删除标记条件
        lqw.eq(WaterfeeMeterReadingRecord::getDelFlag, "0");
        // 添加租户条件
        // lqw.eq(WaterfeeMeterReadingRecord::getTenantId, "000000");
        // 添加排序条件
        lqw.orderByDesc(WaterfeeMeterReadingRecord::getReadingTime);

        // 使用XML中定义的查询
        Page<MeterReadingRecordVo> page = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(page);
    }

    /**
     * 查询抄表记录列表
     *
     * @param record 抄表记录
     * @return 抄表记录
     */
    @Override
    public List<MeterReadingRecordVo> queryList(WaterfeeMeterReadingRecord record) {
        return baseMapper.selectVoList(this.buildQueryWrapper(record));
    }

    private com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<WaterfeeMeterReadingRecord> buildQueryWrapper(
        WaterfeeMeterReadingRecord record) {
        com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<WaterfeeMeterReadingRecord> lqw = new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<>();
        lqw.eq(record.getRecordId() != null, WaterfeeMeterReadingRecord::getRecordId, record.getRecordId());
        lqw.eq(record.getMeterNo() != null, WaterfeeMeterReadingRecord::getMeterNo, record.getMeterNo());
        lqw.eq(record.getMeterType() != null, WaterfeeMeterReadingRecord::getMeterType, record.getMeterType());
        lqw.eq(record.getTaskId() != null, WaterfeeMeterReadingRecord::getTaskId, record.getTaskId());
        lqw.eq(record.getSourceType() != null, WaterfeeMeterReadingRecord::getSourceType, record.getSourceType());
        lqw.eq(record.getManualId() != null, WaterfeeMeterReadingRecord::getManualId, record.getManualId());
        lqw.eq(record.getMeterBookId() != null, WaterfeeMeterReadingRecord::getMeterBookId, record.getMeterBookId());
        lqw.eq(record.getIsAudited() != null, WaterfeeMeterReadingRecord::getIsAudited, record.getIsAudited());
        lqw.eq(record.getIsPending() != null, WaterfeeMeterReadingRecord::getIsPending, record.getIsPending());
        return lqw;
    }

    /**
     * 新增抄表记录
     *
     * @param record 抄表记录
     * @return 结果
     */
    @Override
    public Boolean insertByEntity(WaterfeeMeterReadingRecord record) {
        return baseMapper.insert(record) > 0;
    }

    /**
     * 修改抄表记录
     *
     * @param record 抄表记录
     * @return 结果
     */
    @Override
    public Boolean updateByEntity(WaterfeeMeterReadingRecord record) {
        return baseMapper.updateById(record) > 0;
    }

    /**
     * 批量删除抄表记录
     *
     * @param ids 需要删除的抄表记录主键
     * @return 结果
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否允许删除
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 根据水表编号查询最新一次的抄表记录
     *
     * @param meterNo 水表编号
     * @return 抄表记录
     */
    @Override
    public MeterReadingRecordVo queryLatestByMeterNo(String meterNo) {
        return baseMapper.selectLatestByMeterNo(meterNo);
    }

    /**
     * 根据水表编号查询最新一次的抄表记录（实体）
     *
     * @param meterNo 水表编号
     * @return 抄表记录
     */
    @Override
    public WaterfeeMeterReadingRecord queryLatestEntityByMeterNo(String meterNo) {
        return baseMapper.selectLatestEntityByMeterNo(meterNo);
    }

    /**
     * 批量查询多个水表的最新抄表记录（优化版本）
     *
     * @param meterNos 水表编号列表
     * @return 水表编号到最新记录的映射
     */
    @Override
    public Map<String, WaterfeeMeterReadingRecord> batchQueryLatestEntityByMeterNos(List<String> meterNos) {
        if (meterNos == null || meterNos.isEmpty()) {
            return new HashMap<>();
        }

        try {
            // 批量查询最新记录
            List<WaterfeeMeterReadingRecord> records = baseMapper.batchSelectLatestEntityByMeterNos(meterNos);

            // 转换为Map，便于快速查找
            return records.stream()
                .collect(Collectors.toMap(
                    WaterfeeMeterReadingRecord::getMeterNo,
                    record -> record,
                    (existing, replacement) -> existing // 如果有重复，保留第一个
                ));
        } catch (Exception e) {
            log.error("批量查询最新抄表记录失败，水表编号列表：{}", meterNos, e);
            return new HashMap<>();
        }
    }

    /**
     * 查询水表关联信息
     *
     * @param meterNo 水表编号
     * @return 水表关联信息
     */
    @Override
    public Map<String, Object> queryMeterInfo(String meterNo) {
        Map<String, Object> result = new HashMap<>();

        // 查询水表信息
        String sql = "SELECT wm.meter_no, wm.user_id, u.user_no, u.user_name, " +
            "mb.book_id, mb.book_name, ba.area_id, ba.area_name " +
            "FROM waterfee_meter wm " +
            "LEFT JOIN waterfee_user u ON wm.user_id = u.user_id " +
            "LEFT JOIN waterfee_meter_book mb ON wm.book_id = mb.book_id " +
            "LEFT JOIN waterfee_business_area ba ON mb.business_area_id = ba.area_id " +
            "WHERE wm.meter_no = ? AND wm.del_flag = '0'";

        try {
            Map<String, Object> meterInfo = jdbcTemplate.queryForMap(sql, meterNo);
            result.put("success", true);
            result.put("data", meterInfo);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "未找到水表信息，请检查水表编号是否正确");
        }

        return result;
    }

    /**
     * 批量新增抄表记录（优化版本）
     *
     * @param bos 抄表记录业务对象列表
     * @return 成功插入的记录数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchInsertByBos(List<MeterReadingRecordBo> bos) {
        if (bos == null || bos.isEmpty()) {
            return 0;
        }

        try {
            // 1. 收集所有需要查询的水表编号
            List<String> meterNos = bos.stream()
                .map(MeterReadingRecordBo::getMeterNo)
                .distinct()
                .collect(Collectors.toList());

            // 2. 批量查询所有水表的最新记录（一次查询替代N次查询）
            Map<String, WaterfeeMeterReadingRecord> latestRecordMap =
                batchQueryLatestEntityByMeterNos(meterNos);

            // 3. 批量查询水表信息（避免重复查询）
            Map<String, WaterfeeMeterVo> meterInfoMap = new HashMap<>();
            for (String meterNo : meterNos) {
                if (!latestRecordMap.containsKey(meterNo)) {
                    // 只查询没有历史记录的水表信息
                    WaterfeeMeterVo meterInfo = waterfeeMeterService.queryByNo(meterNo);
                    if (meterInfo != null) {
                        meterInfoMap.put(meterNo, meterInfo);
                    }
                }
            }

            // 4. 批量查询抄表任务信息
            Map<Long, Long> meterBookToTaskMap = new HashMap<>();
            for (WaterfeeMeterVo meterInfo : meterInfoMap.values()) {
                if (meterInfo.getMeterBookId() != null && !meterBookToTaskMap.containsKey(meterInfo.getMeterBookId())) {
                    WaterfeeReadingTaskBo taskBo = new WaterfeeReadingTaskBo();
                    taskBo.setMeterBookId(meterInfo.getMeterBookId());
                    List<WaterfeeReadingTaskVo> tasks = waterfeeReadingTaskService.queryList(taskBo);
                    Long taskId = tasks.isEmpty() ? null : tasks.get(0).getTaskId();
                    meterBookToTaskMap.put(meterInfo.getMeterBookId(), taskId);
                }
            }

            // 5. 批量构建新记录
            List<WaterfeeMeterReadingRecord> newRecords = new ArrayList<>();
            for (MeterReadingRecordBo bo : bos) {
                WaterfeeMeterReadingRecord newRecord = buildNewRecord(bo, latestRecordMap, meterInfoMap, meterBookToTaskMap);
                if (newRecord != null) {
                    newRecords.add(newRecord);
                }
            }

            // 6. 批量插入（如果数据库支持批量插入）
            int successCount = 0;
            for (WaterfeeMeterReadingRecord record : newRecords) {
                if (baseMapper.insert(record) > 0) {
                    successCount++;
                }
            }

            log.info("批量新增抄表记录完成，总数：{}，成功：{}", bos.size(), successCount);
            return successCount;

        } catch (Exception e) {
            log.error("批量新增抄表记录失败", e);
            throw new ServiceException("批量新增抄表记录失败：" + e.getMessage());
        }
    }

    /**
     * 构建新的抄表记录
     */
    private WaterfeeMeterReadingRecord buildNewRecord(MeterReadingRecordBo bo,
                                                      Map<String, WaterfeeMeterReadingRecord> latestRecordMap,
                                                      Map<String, WaterfeeMeterVo> meterInfoMap,
                                                      Map<Long, Long> meterBookToTaskMap) {
        String meterNo = bo.getMeterNo();
        WaterfeeMeterReadingRecord latestRecord = latestRecordMap.get(meterNo);

        WaterfeeMeterReadingRecord newRecord = new WaterfeeMeterReadingRecord();

        if (latestRecord != null) {
            // 有历史记录的情况
            BeanUtils.copyProperties(latestRecord, newRecord);
            newRecord.setLastReading(latestRecord.getCurrentReading());
            newRecord.setLastReadingTime(latestRecord.getReadingTime());
        } else {
            // 没有历史记录的情况
            WaterfeeMeterVo meterInfo = meterInfoMap.get(meterNo);
            if (meterInfo == null) {
                log.warn("未找到水表信息，水表编号：{}", meterNo);
                return null;
            }

            newRecord.setLastReading(0.0);
            newRecord.setLastReadingTime(null);
            newRecord.setMeterType(String.valueOf(meterInfo.getMeterType()));
            newRecord.setMeterNo(meterNo);
            newRecord.setMeterBookId(meterInfo.getMeterBookId());

            Long taskId = meterBookToTaskMap.get(meterInfo.getMeterBookId());
            newRecord.setTaskId(taskId);
        }

        // 设置新记录的数据
        newRecord.setRecordId(null); // 让数据库自动生成
        newRecord.setCurrentReading(bo.getCurrentReading());
        newRecord.setReadingTime(bo.getReadingTime());
        newRecord.setWaterUsage(bo.getCurrentReading() - newRecord.getLastReading());
        newRecord.setSourceType(bo.getSourceType());
        newRecord.setIsAudited(bo.getIsAudited());
        newRecord.setManualId(bo.getManualId());
        newRecord.setRemark(bo.getRemark());
        newRecord.setCreateTime(new Date());
        newRecord.setUpdateTime(new Date());
        newRecord.setDelFlag("0");

        return newRecord;
    }

    /**
     * 新增抄表记录
     *
     * @param bo 抄表记录业务对象
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(MeterReadingRecordBo bo) {
        // 查询该水表的最新抄表记录，作为上期数据
        String meterNo = bo.getMeterNo();
        WaterfeeMeterReadingRecord latestRecord = queryLatestEntityByMeterNo(meterNo);

        // 创建新的抄表记录
        WaterfeeMeterReadingRecord newRecord = new WaterfeeMeterReadingRecord();

        if (latestRecord != null) {
            BeanUtils.copyProperties(latestRecord, newRecord);

            // 设置上期数据
            newRecord.setLastReading(latestRecord.getCurrentReading());
            newRecord.setLastReadingTime(latestRecord.getReadingTime());
        } else {
            // 如果没有找到最新记录，设置上期数据为0
            newRecord.setLastReading(0.0);
            newRecord.setLastReadingTime(null);
            newRecord.setMeterType(String.valueOf(waterfeeMeterService.queryByNo(meterNo).getMeterType()));
            newRecord.setMeterNo(meterNo);

            Long meterBookId = waterfeeMeterService.queryByNo(meterNo).getMeterBookId();
            newRecord.setMeterBookId(meterBookId);

            WaterfeeReadingTaskBo taskBo = new WaterfeeReadingTaskBo();
            taskBo.setMeterBookId(meterBookId);
            List<WaterfeeReadingTaskVo> waterfeeReadingTaskVos = waterfeeReadingTaskService.queryList(taskBo);

            newRecord.setTaskId(waterfeeReadingTaskVos.isEmpty() ? null : waterfeeReadingTaskVos.get(0).getTaskId());

        }

        newRecord.setReadingTime(new Date());
        newRecord.setCurrentReading(bo.getCurrentReading());
        newRecord.setWaterUsage(null);
        newRecord.setRecordId(null);

        // 默认设置为非挂起状态
        newRecord.setIsPending("0");
        newRecord.setPendingReason(null);

        // // 计算水量
        // if (newRecord.getLastReading() != null && newRecord.getCurrentReading() !=
        // null) {
        // double waterUsage = newRecord.getCurrentReading() -
        // newRecord.getLastReading();
        // // 如果有旧表止数，需要加上
        // if (newRecord.getOldMeterStopReading() != null) {
        // waterUsage += newRecord.getOldMeterStopReading();
        // }
        // newRecord.setWaterUsage(waterUsage);
        // }

        newRecord.setSourceType("normal");
        newRecord.setIsAudited("0");

        // 保存抄表记录
        boolean success = baseMapper.insert(newRecord) > 0;
        if (success) {
            bo.setRecordId(newRecord.getRecordId());
        }

        return success;
    }

    /**
     * 根据水表编号查询所有已审核的抄表记录
     *
     * @param meterNo 水表编号
     * @return 抄表记录列表
     */
    @Override
    public List<MeterReadingRecordVo> queryAllByMeterNo(String meterNo) {
        return baseMapper.selectAllByMeterNo(meterNo);
    }

    /**
     * 审核单条抄表记录
     *
     * @param recordId 抄表记录ID
     * @return 结果
     */
    @Override
    public Boolean auditRecord(Long recordId) {
        WaterfeeMeterReadingRecord record = baseMapper.selectById(recordId);
        if (record == null) {
            throw new RuntimeException("抄表记录不存在");
        }

        if (record.getIsAudited() != null && "1".equals(record.getIsAudited())) {
            throw new RuntimeException("抄表记录已审核，不能重复审核");
        }

        // 检查是否为挂起状态
        if (record.getIsPending() != null && "1".equals(record.getIsPending())) {
            throw new RuntimeException("抄表记录处于挂起状态，需先解除挂起后再审核");
        }

        // 设置审核状态为已审核
        record.setIsAudited("1");
        record.setWaterUsage(null);

        baseMapper.updateById(record);

        // 生成账单
        Long[] recordIds = {recordId};
        generateBill(recordIds);
        return true;
    }

    /**
     * 智能表自动审核多条抄表记录
     *
     * @param record 抄表记录
     * @return 结果
     */
    @Override
    public List<Long> auditRecordRtnBillIds(WaterfeeMeterReadingRecord record) {
        if (record == null) {
            throw new RuntimeException("抄表记录不存在");
        }

        // 检查是否为挂起状态
        if (record.getIsPending() != null && "1".equals(record.getIsPending())) {
            throw new RuntimeException("抄表记录处于挂起状态，需先解除挂起后再审核");
        }

        // 智能表自动审核
        if (record.getMeterType() != null && "2".equals(record.getMeterType())) {
            // 如果是智能表，直接设置为已审核状态
            // 智能表设置审核状态为已审核
            record.setIsAudited("1");
            record.setWaterUsage(null);

            baseMapper.updateById(record);

            // 生成账单
            Long[] recordIds = {record.getRecordId()};
            return generateBill(recordIds);
        } else if (record.getMeterType() != null && "1".equals(record.getMeterType())) {
            // 机械表不自动审核，返回空列表
            return Collections.emptyList();
        } else {
            throw new RuntimeException("未知的水表类型");
        }
    }

    /**
     * 按表册审核抄表记录
     *
     * @param meterBookId 表册ID
     * @param taskId      任务ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean auditRecordsByBook(Long meterBookId, Long taskId) {
        // 查询表册下所有当次任务的抄表记录
        List<WaterfeeMeterReadingRecord> records = meterReadingCommonService.selectByBookIdAndTaskId(meterBookId,
            taskId);
        if (records.isEmpty()) {
            throw new RuntimeException("未找到相关抄表记录");
        }

        int successCount = 0;
        List<Long> recordIds = new ArrayList<>();

        // 批量审核所有记录
        for (WaterfeeMeterReadingRecord record : records) {
            if (record.getIsPending() != null && "1".equals(record.getIsPending())) {
                // 如果是待审核状态，跳过
                continue;
            }
            if (record.getIsAudited() != null && "1".equals(record.getIsAudited())) {
                // 如果已经审核过，跳过
                successCount++;
                continue;
            }
            // 设置审核状态为已审核
            record.setIsAudited("1");
            record.setWaterUsage(null);
            recordIds.add(record.getRecordId());

            if (baseMapper.updateById(record) > 0) {
                successCount++;
            }
        }

        if (successCount == records.size()) {
            // 所有记录都审核成功，更新抄表任务状态为已完成
            meterReadingCommonService.updateTaskAuditStatus(taskId, "1");
        }

        // 生成账单
        generateBill(recordIds.toArray(new Long[0]));
        return successCount > 0;
    }

    /**
     * 审核通过后生成账单
     */
    private List<Long> generateBill(Long[] recordIds) {
        List<Long> billIds = new ArrayList<>();

        for (Long recordId : recordIds) {
            try {
                Long billId = processSingleRecord(recordId);
                billIds.add(billId);
            } catch (Exception e) {
                log.error("处理抄表记录失败，ID: {}", recordId, e);
                throw e;
            }
        }

        return billIds;
    }

    /**
     * 处理单个抄表记录（修复版本）
     */
    private Long processSingleRecord(Long recordId) {
        // 1. 获取基础数据
        WaterfeeMeterReadingRecord record = getValidRecord(recordId);
        WaterfeeMeterVo meter = getValidMeter(record.getMeterNo());
        WaterfeeUser user = getValidUser(meter.getUserId());

        //深拷贝user对象
        WaterfeeUser userCopy = new WaterfeeUser(user);

        // 2. 保存用户原始的阶梯用水量（用于账单详情JSON生成）
        Double originalLadderUsage = user.getLadderUsage();

        // 3. 计算水费
        BillCalculationResult calculationResult = calculateWaterBill(record, user);

        // 4. 更新用户用水量
        updateUserWaterUsage(user, record);

        // 5. 创建并保存账单（传递原始阶梯用水量）
        return createAndSaveBill(record, userCopy, meter, calculationResult.getPriceConfigId(),
            calculationResult.getTotalCharge(), record.getWaterUsage(), originalLadderUsage);
    }

    /**
     * 获取有效的抄表记录
     */
    private WaterfeeMeterReadingRecord getValidRecord(Long recordId) {
        WaterfeeMeterReadingRecord record = baseMapper.selectById(recordId);
        if (record == null) {
            throw new RuntimeException("抄表记录不存在，ID: " + recordId);
        }
        return record;
    }

    /**
     * 获取有效的水表信息
     */
    private WaterfeeMeterVo getValidMeter(String meterNo) {
        WaterfeeMeterVo meter = waterfeeMeterService.queryByNo(meterNo);
        if (meter == null) {
            throw new RuntimeException("水表信息不存在: " + meterNo);
        }
        return meter;
    }

    /**
     * 获取有效的用户信息
     */
    private WaterfeeUser getValidUser(Long userId) {
        WaterfeeUser user = waterfeeUserService.queryById(userId);
        if (user == null) {
            throw new RuntimeException("用户信息不存在，用户ID: " + userId);
        }
        return user;
    }

    /**
     * 计算水费账单
     */
    private BillCalculationResult calculateWaterBill(WaterfeeMeterReadingRecord record, WaterfeeUser user) {
        Long priceConfigId = Long.valueOf(user.getBillingMethod());
        List<WaterfeePriceTier> tiers = getPriceTiers(priceConfigId);

        BigDecimal waterUsage = BigDecimal.valueOf(record.getWaterUsage());
        BigDecimal ladderUsage = BigDecimal.valueOf(user.getLadderUsage() == null ? 0.0 : user.getLadderUsage());

        return calculateTieredBilling(tiers, waterUsage, ladderUsage, user, priceConfigId);
    }

    /**
     * 获取价格阶梯配置（优化版本：使用缓存）
     */
    private List<WaterfeePriceTier> getPriceTiers(Long priceConfigId) {
        // 先尝试从缓存获取
        List<WaterfeePriceTier> cachedTiers = cacheService.getCachedPriceTiers(priceConfigId);
        if (cachedTiers != null) {
            return cachedTiers;
        }

        // 缓存未命中，从数据库查询
        List<WaterfeePriceTier> tiers = waterfeePriceTierMapper.selectList(
            new LambdaQueryWrapper<WaterfeePriceTier>()
                .eq(WaterfeePriceTier::getPriceConfigId, priceConfigId)
                .orderByAsc(WaterfeePriceTier::getTierNumber));

        // 缓存查询结果
        if (!tiers.isEmpty()) {
            cacheService.cachePriceTiers(priceConfigId, tiers);
        }

        return tiers;
    }

    /**
     * 计算阶梯计费
     */
    private BillCalculationResult calculateTieredBilling(List<WaterfeePriceTier> tiers,
                                                         BigDecimal waterUsage,
                                                         BigDecimal ladderUsage,
                                                         WaterfeeUser user,
                                                         Long priceConfigId) {
        BigDecimal totalCharge = BigDecimal.ZERO;
        BigDecimal currentUsage = waterUsage;
        BigDecimal currentLadderUsage = ladderUsage;

        Map<Integer, BigDecimal> tierUsageMap = new HashMap<>();
        Map<Integer, BigDecimal> tierAmountMap = new HashMap<>();

        for (WaterfeePriceTier tier : tiers) {
            TierCalculationResult tierResult = calculateSingleTier(tier, currentUsage, currentLadderUsage, user, priceConfigId);

            if (tierResult.getTierUsage().compareTo(BigDecimal.ZERO) > 0) {
                totalCharge = totalCharge.add(tierResult.getTierAmount());
                currentUsage = currentUsage.subtract(tierResult.getTierUsage());
                currentLadderUsage = currentLadderUsage.add(tierResult.getTierUsage());

                tierUsageMap.put(tier.getTierNumber(), tierResult.getTierUsage());
                tierAmountMap.put(tier.getTierNumber(), tierResult.getTierAmount());

                log.info("阶梯 {}: 用量 = {}, 费用 = {}", tier.getTierNumber(),
                    tierResult.getTierUsage(), tierResult.getTierAmount());
            }

            if (currentUsage.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }
        }

        return new BillCalculationResult(totalCharge, priceConfigId, tierUsageMap, tierAmountMap);
    }

    /**
     * 计算单个阶梯的费用（增强调试版本）
     */
    private TierCalculationResult calculateSingleTier(WaterfeePriceTier tier,
                                                      BigDecimal currentUsage,
                                                      BigDecimal ladderUsage,
                                                      WaterfeeUser user,
                                                      Long priceConfigId) {
        // 获取阶梯范围（考虑人口因素）
        TierRange tierRange = calculateTierRange(tier, user, priceConfigId);

        // 计算阶梯单价
        BigDecimal tierPrice = calculateTierPrice(tier);

        // 计算本阶梯可用量
        BigDecimal tierAvailable = calculateTierAvailable(tierRange, currentUsage, ladderUsage);

        BigDecimal tierUsage = BigDecimal.ZERO;
        BigDecimal tierAmount = BigDecimal.ZERO;

        if (tierAvailable.compareTo(BigDecimal.ZERO) > 0) {
            tierUsage = currentUsage.min(tierAvailable);
            tierAmount = tierUsage.multiply(tierPrice);
        }

        // 详细调试日志
        log.debug("阶梯{}计算详情: 范围[{}-{}], 累计用量={}, 本次用量={}, 可用量={}, 实际用量={}, 单价={}, 费用={}",
            tier.getTierNumber(),
            tierRange.getStart(),
            tierRange.getEnd(),
            ladderUsage,
            currentUsage,
            tierAvailable,
            tierUsage,
            tierPrice,
            tierAmount);

        return new TierCalculationResult(tierUsage, tierAmount);
    }

    /**
     * 计算阶梯范围（考虑人口因素）
     */
    private TierRange calculateTierRange(WaterfeePriceTier tier, WaterfeeUser user, Long priceConfigId) {
        BigDecimal start = tier.getStartQuantity();
        BigDecimal end = tier.getEndQuantity();

        // 如果是居民用户，且阶梯价格按人口计算
        if ("resident".equals(user.getUseWaterNature())) {
            WaterfeePriceConfig priceConfig = waterfeePriceConfigMapper.selectById(priceConfigId);
            if (priceConfig.getIsPopulation() == 1 && user.getUseWaterNumber() != null) {
                BigDecimal multiplier = BigDecimal.valueOf(user.getUseWaterNumber());
                start = start.multiply(multiplier);
                if (end != null) {
                    end = end.multiply(multiplier);
                }
            }
        }

        return new TierRange(start, end);
    }

    /**
     * 计算阶梯总价格
     */
    private BigDecimal calculateTierPrice(WaterfeePriceTier tier) {
        return tier.getPrice()
            .add(tier.getWaterResourceTax())
            .add(tier.getSewageTreatmentFee())
            .add(tier.getGarbageDisposalFee())
            .add(tier.getSanitationFee())
            .add(tier.getMaintenanceFund());
    }

    /**
     * 计算阶梯可用量（修复版本）
     */
    private BigDecimal calculateTierAvailable(TierRange tierRange, BigDecimal currentUsage, BigDecimal ladderUsage) {
        BigDecimal start = tierRange.getStart(); // 阶梯起始量
        BigDecimal end = tierRange.getEnd();     // 阶梯结束量（可能是 null）

        // 如果用户累计用水量还没有达到当前阶梯的起始量，则当前阶梯不可用
        if (ladderUsage.compareTo(start) < 0) {
            return BigDecimal.ZERO;
        }

        if (end == null) {
            // 最高阶梯（无上限）：可以使用所有剩余的用水量
            return currentUsage;
        } else {
            // 有上限的阶梯：计算该阶梯还能使用多少
            BigDecimal tierCapacity = end.subtract(start);           // 阶梯总容量
            BigDecimal usedInThisTier = ladderUsage.subtract(start); // 在该阶梯中已使用的量
            BigDecimal remainingInTier = tierCapacity.subtract(usedInThisTier); // 该阶梯剩余容量

            // 返回该阶梯剩余容量和当前用水量的较小值
            return remainingInTier.max(BigDecimal.ZERO).min(currentUsage);
        }
    }


    /**
     * 更新用户用水量
     */
    private void updateUserWaterUsage(WaterfeeUser user, WaterfeeMeterReadingRecord record) {
        Date readingTime = record.getReadingTime();
        if (readingTime == null) {
            return;
        }

        double waterUsage = record.getWaterUsage();

        // 更新用户当前季度和总用水量,当前用水量每季度清零
        updateQuarterlyUsage(user, readingTime, waterUsage);

        // 总用水量始终累加
        user.setTotalUsage((user.getTotalUsage() == null ? 0.0 : user.getTotalUsage()) + waterUsage);

        // 更新用户信息
        waterfeeUserService.updateById(user);
    }

    /**
     * 更新季度用水量
     */
    private void updateQuarterlyUsage(WaterfeeUser user, Date readingTime, double waterUsage) {
        // 获取当前记录所处季度的起止时间
        Date[] quarterRange = getQuarterRange(readingTime);
        Date quarterStart = quarterRange[0];

        Date lastResetTime = user.getLastQuarterResetTime();
        boolean needReset = lastResetTime == null || lastResetTime.before(quarterStart);

        if (needReset) {
            // 是该季度的首次重置，更新季度用水量和重置时间
            user.setLadderUsage(waterUsage);
            user.setLastQuarterResetTime(readingTime);
        } else {
            // 非首次，季度用水量累加
            user.setLadderUsage((user.getLadderUsage() == null ? 0.0 : user.getLadderUsage()) + waterUsage);
        }
    }

    /**
     * 创建或更新季度账单（修复版本）
     *
     * @param record              抄表记录
     * @param user                用户信息
     * @param meter               水表信息
     * @param priceConfigId       价格配置ID
     * @param totalCharge         总费用
     * @param waterUsage          水量
     * @param originalLadderUsage 原始阶梯用水量（用于账单详情JSON生成）
     * @return 账单ID
     */
    private Long createAndSaveBill(WaterfeeMeterReadingRecord record, WaterfeeUser user, WaterfeeMeterVo meter,
                                   Long priceConfigId, BigDecimal totalCharge, double waterUsage, Double originalLadderUsage) {

        // 获取当前记录所处季度的起止时间
        Date[] quarterRange = getQuarterRange(record.getReadingTime());
        Date quarterStart = quarterRange[0];
        Date quarterEnd = quarterRange[1];

        // 查找当季度是否已有账单
        WaterfeeBillVo existingBill = waterfeeBillService.queryByCustomerIdAndQuarter(
            user.getUserId(), quarterStart, quarterEnd);

        if (existingBill != null) {
            // 更新现有账单，累计金额和用水量
            return updateExistingQuarterlyBill(existingBill, record, user, meter, priceConfigId, totalCharge, waterUsage, originalLadderUsage);
        } else {
            // 创建新的季度账单
            return createNewQuarterlyBill(record, user, meter, priceConfigId, totalCharge, waterUsage, quarterStart, quarterEnd, originalLadderUsage);
        }
    }

    /**
     * 更新现有季度账单，累计金额和用水量（修复版本）
     *
     * @param existingBill        现有账单
     * @param record              抄表记录
     * @param user                用户信息
     * @param meter               水表信息
     * @param priceConfigId       价格配置ID
     * @param totalCharge         本次费用
     * @param waterUsage          本次用水量
     * @param originalLadderUsage 原始阶梯用水量（用于账单详情JSON生成）
     * @return 账单ID
     */
    private Long updateExistingQuarterlyBill(WaterfeeBillVo existingBill, WaterfeeMeterReadingRecord record,
                                             WaterfeeUser user, WaterfeeMeterVo meter, Long priceConfigId,
                                             BigDecimal totalCharge, double waterUsage, Double originalLadderUsage) {

        // 预先计算所有需要的值，减少重复计算和空指针检查
        BigDecimal existingConsumption = existingBill.getConsumptionVolume() != null ?
            existingBill.getConsumptionVolume() : BigDecimal.ZERO;
        BigDecimal existingTotal = existingBill.getTotalAmount() != null ?
            existingBill.getTotalAmount() : BigDecimal.ZERO;
        BigDecimal existingBaseCharge = existingBill.getBaseChargeAmount() != null ?
            existingBill.getBaseChargeAmount() : BigDecimal.ZERO;
        BigDecimal amountPaid = existingBill.getAmountPaid() != null ?
            existingBill.getAmountPaid() : BigDecimal.ZERO;

        // 计算累计值
        BigDecimal newConsumptionVolume = existingConsumption.add(BigDecimal.valueOf(waterUsage));
        BigDecimal newTotalAmount = existingTotal.add(totalCharge);
        BigDecimal newBaseChargeAmount = existingBaseCharge.add(totalCharge);
        BigDecimal newBalanceDue = newTotalAmount.subtract(amountPaid);

        // 一次性计算分项费用
        BillFeeBreakdown feeBreakdown = calculateFeeBreakdown(user, priceConfigId, newConsumptionVolume.doubleValue());

        // 构建更新的账单信息
        WaterfeeBillBo updateBill = new WaterfeeBillBo();
        updateBill.setBillId(existingBill.getBillId());
        updateBill.setConsumptionVolume(newConsumptionVolume);
        updateBill.setTotalAmount(newTotalAmount);
        updateBill.setBaseChargeAmount(newBaseChargeAmount);
        updateBill.setBalanceDue(newBalanceDue);

        // 更新计费周期结束时间和读数信息
        updateBill.setBillingPeriodEnd(record.getReadingTime());
        updateBill.setCurrentReadingId(record.getRecordId());
        updateBill.setCurrentReadingValue(BigDecimal.valueOf(record.getCurrentReading()));

        // 设置分项费用
        updateBill.setWaterResourceTax(feeBreakdown.getWaterResourceTax());
        updateBill.setSewageTreatmentFee(feeBreakdown.getSewageTreatmentFee());
        updateBill.setWaterBillOnly(feeBreakdown.getWaterBillOnly());

        // 更新账单详情JSON（使用原始阶梯用水量）
        String detailJson = buildBillDetailJsonWithOriginalLadderUsage(user, priceConfigId,
            newConsumptionVolume.doubleValue(), newTotalAmount, originalLadderUsage);
        updateBill.setDetailJson(detailJson);

        // 优化账单状态判断
        String billStatus = determineBillStatus(newTotalAmount, amountPaid);
        updateBill.setBillStatus(billStatus);

        // 执行更新
        waterfeeBillService.updateByBo(updateBill);
        return existingBill.getBillId();
    }

    /**
     * 确定账单状态（优化方法）
     */
    private String determineBillStatus(BigDecimal totalAmount, BigDecimal amountPaid) {
        if (totalAmount.compareTo(BigDecimal.ZERO) == 0) {
            return "PAID";
        } else if (totalAmount.compareTo(amountPaid) <= 0) {
            return "PAID";
        } else if (amountPaid.compareTo(BigDecimal.ZERO) > 0) {
            return "PARTIALLY_PAID";
        } else {
            return "ISSUED";
        }
    }

    /**
     * 创建新的季度账单（修复版本）
     *
     * @param record              抄表记录
     * @param user                用户信息
     * @param meter               水表信息
     * @param priceConfigId       价格配置ID
     * @param totalCharge         总费用
     * @param waterUsage          水量
     * @param quarterStart        季度开始时间
     * @param quarterEnd          季度结束时间
     * @param originalLadderUsage 原始阶梯用水量（用于账单详情JSON生成）
     * @return 账单ID
     */
    private Long createNewQuarterlyBill(WaterfeeMeterReadingRecord record, WaterfeeUser user, WaterfeeMeterVo meter,
                                        Long priceConfigId, BigDecimal totalCharge, double waterUsage,
                                        Date quarterStart, Date quarterEnd, Double originalLadderUsage) {

        // 构建基础账单信息
        WaterfeeBillBo bill = buildBasicBillInfo(record, user, meter, priceConfigId, totalCharge, waterUsage);

        // 设置季度时间范围
        bill.setBillingPeriodStart(quarterStart);
        bill.setBillingPeriodEnd(quarterEnd);

        // 设置账单月份为季度格式（例如：2025Q1）
        String quarterLabel = getQuarterLabel(record.getReadingTime());
        bill.setBillMonth(quarterLabel);

        // 构建账单详情（使用原始阶梯用水量）
        String detailJson = buildBillDetailJsonWithOriginalLadderUsage(user, priceConfigId,
            waterUsage, totalCharge, originalLadderUsage);
        bill.setDetailJson(detailJson);

        // 插入账单
        waterfeeBillService.insertByBo(bill);
        return bill.getBillId();
    }

    /**
     * 获取季度标签（例如：2025Q1）
     */
    private String getQuarterLabel(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int year = cal.get(Calendar.YEAR);
        int month = cal.get(Calendar.MONTH) + 1;

        int quarter;
        if (month == 12 || month == 1 || month == 2) {
            quarter = 4; // 第四季度
            if (month == 1 || month == 2) {
                year = year; // 1-2月属于当年第四季度的延续，但标记为当年
            }
        } else if (month >= 3 && month <= 5) {
            quarter = 1; // 第一季度
        } else if (month >= 6 && month <= 8) {
            quarter = 2; // 第二季度
        } else {
            quarter = 3; // 第三季度
        }

        return year + "Q" + quarter;
    }

    /**
     * 构建基础账单信息
     */
    private WaterfeeBillBo buildBasicBillInfo(WaterfeeMeterReadingRecord record, WaterfeeUser user,
                                              WaterfeeMeterVo meter, Long priceConfigId,
                                              BigDecimal totalCharge, double waterUsage) {
        WaterfeeBillBo bill = new WaterfeeBillBo();

        // 基础信息
        bill.setBillNumber("BILL-" + IdUtil.getSnowflake(1, 1).nextIdStr());
        bill.setCustomerId(user.getUserId());
        bill.setUserNo(user.getUserNo());
        bill.setUserName(user.getUserName());
        bill.setAddress(user.getAddress());
        bill.setMeterId(meter.getMeterId());
        bill.setMeterBookId(meter.getMeterBookId());
        bill.setPricePlanId(priceConfigId);

        // 时间信息
        bill.setBillingPeriodStart(record.getLastReadingTime());
        bill.setBillingPeriodEnd(record.getReadingTime());
        bill.setBillingIssueDate(new Date());
        bill.setBillingDueDate(DateUtils.addDays(new Date(), 15));
        bill.setBillMonth(DateFormatUtils.format(record.getReadingTime(), "yyyyMM"));

        // 抄表信息
        bill.setCurrentReadingId(record.getRecordId());
        bill.setCurrentReadingValue(BigDecimal.valueOf(record.getCurrentReading()));
        bill.setPreviousReadingValue(BigDecimal.valueOf(record.getLastReading()));
        bill.setConsumptionVolume(BigDecimal.valueOf(waterUsage));
        bill.setConsumptionUnit("m³");

        // 计算分项费用
        BillFeeBreakdown feeBreakdown = calculateFeeBreakdown(user, priceConfigId, waterUsage);

        // 费用信息
        bill.setBaseChargeAmount(totalCharge);
        bill.setTotalAmount(totalCharge);
        bill.setAmountPaid(BigDecimal.ZERO);


        //如果账单金额<=0，设置账单状态为“PAID”
        if (totalCharge.compareTo(BigDecimal.ZERO) == 0) {
            bill.setBillStatus("PAID");
        } else if (totalCharge.compareTo(BigDecimal.ZERO) < 0) {
            // 如果账单金额小于0，异常
            throw new RuntimeException("账单金额异常，不能小于0: " + totalCharge);
        } else {
            bill.setBillStatus("ISSUED");
        }

        // 设置分项费用
        bill.setWaterResourceTax(feeBreakdown.getWaterResourceTax());
        bill.setSewageTreatmentFee(feeBreakdown.getSewageTreatmentFee());
        bill.setWaterBillOnly(feeBreakdown.getWaterBillOnly());

        return bill;
    }

    /**
     * 计算费用分解
     */
    private BillFeeBreakdown calculateFeeBreakdown(WaterfeeUser user, Long priceConfigId, double waterUsage) {
        List<WaterfeePriceTier> tiers = getPriceTiers(priceConfigId);
        BigDecimal currentUsage = BigDecimal.valueOf(waterUsage);
        BigDecimal ladderUsage = BigDecimal.valueOf(user.getLadderUsage() == null ? 0.0 : user.getLadderUsage());

        BigDecimal totalWaterResourceTax = BigDecimal.ZERO;
        BigDecimal totalSewageTreatmentFee = BigDecimal.ZERO;
        BigDecimal totalWaterBillOnly = BigDecimal.ZERO;

        for (WaterfeePriceTier tier : tiers) {
            TierCalculationResult tierResult = calculateSingleTier(tier, currentUsage, ladderUsage, user, priceConfigId);

            if (tierResult.getTierUsage().compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal tierUsage = tierResult.getTierUsage();
                totalWaterResourceTax = totalWaterResourceTax.add(tierUsage.multiply(tier.getWaterResourceTax()));
                totalSewageTreatmentFee = totalSewageTreatmentFee.add(tierUsage.multiply(tier.getSewageTreatmentFee()));
                totalWaterBillOnly = totalWaterBillOnly.add(tierUsage.multiply(tier.getPrice()));

                currentUsage = currentUsage.subtract(tierUsage);
                ladderUsage = ladderUsage.add(tierUsage);
            }

            if (currentUsage.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }
        }

        return new BillFeeBreakdown(totalWaterResourceTax, totalSewageTreatmentFee, totalWaterBillOnly);
    }

    /**
     * 构建账单详情JSON
     */
    private String buildBillDetailJson(WaterfeeUser user, Long priceConfigId, double waterUsage, BigDecimal totalCharge) {
        List<WaterfeePriceTier> tiers = getPriceTiers(priceConfigId);
        List<Map<String, Object>> tierDetailsList = buildTierDetailsList(tiers, user, priceConfigId, waterUsage);

        Map<String, Object> detailMap = new HashMap<>();
        detailMap.put("tierDetails", tierDetailsList);
        detailMap.put("totalConsumption", waterUsage);
        detailMap.put("totalAmount", totalCharge);
        detailMap.put("userId", user.getUserId());
        detailMap.put("userNo", user.getUserNo());
        detailMap.put("ladderUsage", user.getLadderUsage());

        return JSON.toJSONString(detailMap);
    }

    /**
     * 构建账单详情JSON（使用原始阶梯用水量）
     */
    private String buildBillDetailJsonWithOriginalLadderUsage(WaterfeeUser user, Long priceConfigId,
                                                              double waterUsage, BigDecimal totalCharge,
                                                              Double originalLadderUsage) {
        List<WaterfeePriceTier> tiers = getPriceTiers(priceConfigId);
        List<Map<String, Object>> tierDetailsList = buildTierDetailsListWithOriginalLadderUsage(
            tiers, user, priceConfigId, waterUsage, originalLadderUsage);

        Map<String, Object> detailMap = new HashMap<>();
        detailMap.put("tierDetails", tierDetailsList);
        detailMap.put("totalConsumption", waterUsage);
        detailMap.put("totalAmount", totalCharge);
        detailMap.put("userId", user.getUserId());
        detailMap.put("userNo", user.getUserNo());
        detailMap.put("ladderUsage", originalLadderUsage); // 使用原始阶梯用水量

        return JSON.toJSONString(detailMap);
    }

    /**
     * 构建阶梯详情列表
     */
    private List<Map<String, Object>> buildTierDetailsList(List<WaterfeePriceTier> tiers,
                                                           WaterfeeUser user,
                                                           Long priceConfigId,
                                                           double waterUsage) {
        List<Map<String, Object>> tierDetailsList = new ArrayList<>();
        BigDecimal currentUsage = BigDecimal.valueOf(waterUsage);
        BigDecimal ladderUsage = BigDecimal.valueOf(user.getLadderUsage() == null ? 0.0 : user.getLadderUsage());

        for (WaterfeePriceTier tier : tiers) {
            Map<String, Object> tierDetail = buildSingleTierDetail(tier, user, priceConfigId, currentUsage, ladderUsage);
            tierDetailsList.add(tierDetail);

            // 更新剩余用量
            BigDecimal tierUsage = (BigDecimal) tierDetail.get("usage");
            currentUsage = currentUsage.subtract(tierUsage);
            ladderUsage = ladderUsage.add(tierUsage);

            if (currentUsage.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }
        }

        return tierDetailsList;
    }

    /**
     * 构建阶梯详情列表（使用原始阶梯用水量）
     */
    private List<Map<String, Object>> buildTierDetailsListWithOriginalLadderUsage(List<WaterfeePriceTier> tiers,
                                                                                  WaterfeeUser user,
                                                                                  Long priceConfigId,
                                                                                  double waterUsage,
                                                                                  Double originalLadderUsage) {
        List<Map<String, Object>> tierDetailsList = new ArrayList<>();
        BigDecimal currentUsage = BigDecimal.valueOf(waterUsage);
        BigDecimal ladderUsage = BigDecimal.valueOf(originalLadderUsage == null ? 0.0 : originalLadderUsage);

        for (WaterfeePriceTier tier : tiers) {
            Map<String, Object> tierDetail = buildSingleTierDetail(tier, user, priceConfigId, currentUsage, ladderUsage);
            tierDetailsList.add(tierDetail);

            // 更新剩余用量
            BigDecimal tierUsage = (BigDecimal) tierDetail.get("usage");
            currentUsage = currentUsage.subtract(tierUsage);
            ladderUsage = ladderUsage.add(tierUsage);

            if (currentUsage.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }
        }

        return tierDetailsList;
    }

    /**
     * 构建单个阶梯详情
     */
    private Map<String, Object> buildSingleTierDetail(WaterfeePriceTier tier, WaterfeeUser user,
                                                      Long priceConfigId, BigDecimal currentUsage,
                                                      BigDecimal ladderUsage) {
        Map<String, Object> tierDetail = new HashMap<>();

        // 基础配置信息
        tierDetail.put("tierNumber", tier.getTierNumber());
        tierDetail.put("startQuantity", tier.getStartQuantity());
        tierDetail.put("endQuantity", tier.getEndQuantity());
        tierDetail.put("price", tier.getPrice());
        tierDetail.put("waterResourceTax", tier.getWaterResourceTax());
        tierDetail.put("sewageTreatmentFee", tier.getSewageTreatmentFee());
        tierDetail.put("garbageDisposalFee", tier.getGarbageDisposalFee());
        tierDetail.put("sanitationFee", tier.getSanitationFee());
        tierDetail.put("maintenanceFund", tier.getMaintenanceFund());

        // 计算实际范围和价格
        TierRange tierRange = calculateTierRange(tier, user, priceConfigId);
        BigDecimal tierPrice = calculateTierPrice(tier);

        tierDetail.put("actualStartQuantity", tierRange.getStart());
        tierDetail.put("actualEndQuantity", tierRange.getEnd());
        tierDetail.put("totalPrice", tierPrice);

        // 计算用量和费用
        TierCalculationResult result = calculateSingleTier(tier, currentUsage, ladderUsage, user, priceConfigId);
        tierDetail.put("usage", result.getTierUsage());
        tierDetail.put("amount", result.getTierAmount());

        return tierDetail;
    }

    /**
     * 更新抄表记录
     *
     * @param record 抄表记录
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateRecord(WaterfeeMeterReadingRecord record) {
        // 查询原有抄表记录
        WaterfeeMeterReadingRecord existingRecord = baseMapper.selectById(record.getRecordId());
        if (existingRecord == null) {
            throw new RuntimeException("抄表记录不存在");
        }

        // 检查是否已审核
        if (existingRecord.getIsAudited() != null && "1".equals(existingRecord.getIsAudited())) {
            throw new RuntimeException("已审核的抄表记录不能修改");
        }

        // 保留原有记录的一些字段
        record.setMeterNo(existingRecord.getMeterNo());
        record.setMeterType(existingRecord.getMeterType());
        record.setLastReading(existingRecord.getLastReading());
        record.setLastReadingTime(existingRecord.getLastReadingTime());
        record.setTaskId(existingRecord.getTaskId());

        // 保留挂起状态和原因
        record.setIsPending(existingRecord.getIsPending());
        record.setPendingReason(existingRecord.getPendingReason());

        // 确保不设置 water_usage 字段，让数据库自动计算
        record.setWaterUsage(null);

        // 更新记录
        return baseMapper.updateById(record) > 0;
    }

    /**
     * 根据表册ID查询机械表抄表记录
     *
     * @param meterBookId 表册ID
     * @return 机械表抄表记录列表
     */
    @Override
    public List<MeterReadingRecordVo> queryMechanicalMetersByBookId(Long meterBookId) {
        return meterReadingCommonService.queryMechanicalMetersByBookId(meterBookId);
    }

    /**
     * 根据表册ID查询智能表抄表记录
     *
     * @param meterBookId 表册ID
     * @return 智能表抄表记录列表
     */
    @Override
    public List<MeterReadingRecordVo> queryIntelligentMetersByBookId(Long meterBookId) {
        return meterReadingCommonService.queryIntelligentMetersByBookId(meterBookId);
    }

    /**
     * 查询指定月份的抄表记录列表
     *
     * @param record    抄表记录
     * @param pageQuery 分页参数
     * @return 抄表记录列表
     */
    @Override
    public TableDataInfo<MeterReadingRecordVo> queryCurrentMonthList(WaterfeeMeterReadingRecord record,
                                                                     PageQuery pageQuery) {

        // 构建查询条件
        LambdaQueryWrapper<WaterfeeMeterReadingRecord> lqw = new LambdaQueryWrapper<>();

        // 如果没有指定任务ID，则无法获取月份信息，默认使用当前月份
        if (record.getTaskId() == null) {
            // 设置本月的时间范围
            java.util.Calendar calendar = java.util.Calendar.getInstance();
            // 设置为当月第一天
            calendar.set(java.util.Calendar.DAY_OF_MONTH, 1);
            calendar.set(java.util.Calendar.HOUR_OF_DAY, 0);
            calendar.set(java.util.Calendar.MINUTE, 0);
            calendar.set(java.util.Calendar.SECOND, 0);
            calendar.set(java.util.Calendar.MILLISECOND, 0);
            java.util.Date monthStart = calendar.getTime();

            // 设置为下个月第一天
            calendar.add(java.util.Calendar.MONTH, 1);
            java.util.Date nextMonthStart = calendar.getTime();

            // 添加本月的时间范围条件
            lqw.ge(WaterfeeMeterReadingRecord::getReadingTime, monthStart);
            lqw.lt(WaterfeeMeterReadingRecord::getReadingTime, nextMonthStart);
        } else {
            // 查询任务信息，获取抄表月份
            WaterfeeReadingTask task = meterReadingCommonService.getTaskById(record.getTaskId());

            if (task != null && task.getReadingMonth() != null) {
                // 解析抄表月份（格式：yyyy-MM）
                String[] parts = task.getReadingMonth().split("-");
                if (parts.length == 2) {
                    try {
                        int year = Integer.parseInt(parts[0]);
                        int month = Integer.parseInt(parts[1]) - 1; // 月份从0开始

                        // 设置指定月份的时间范围
                        java.util.Calendar calendar = java.util.Calendar.getInstance();
                        // 设置为指定月份的第一天
                        calendar.set(year, month, 1, 0, 0, 0);
                        calendar.set(java.util.Calendar.MILLISECOND, 0);
                        java.util.Date monthStart = calendar.getTime();

                        // 设置为下个月第一天
                        calendar.add(java.util.Calendar.MONTH, 1);
                        java.util.Date nextMonthStart = calendar.getTime();

                        // 添加指定月份的时间范围条件
                        lqw.ge(WaterfeeMeterReadingRecord::getReadingTime, monthStart);
                        lqw.lt(WaterfeeMeterReadingRecord::getReadingTime, nextMonthStart);
                    } catch (NumberFormatException e) {
                        // 如果解析失败，使用当前月份
                        java.util.Calendar calendar = java.util.Calendar.getInstance();
                        calendar.set(java.util.Calendar.DAY_OF_MONTH, 1);
                        calendar.set(java.util.Calendar.HOUR_OF_DAY, 0);
                        calendar.set(java.util.Calendar.MINUTE, 0);
                        calendar.set(java.util.Calendar.SECOND, 0);
                        calendar.set(java.util.Calendar.MILLISECOND, 0);
                        java.util.Date monthStart = calendar.getTime();

                        calendar.add(java.util.Calendar.MONTH, 1);
                        java.util.Date nextMonthStart = calendar.getTime();

                        lqw.ge(WaterfeeMeterReadingRecord::getReadingTime, monthStart);
                        lqw.lt(WaterfeeMeterReadingRecord::getReadingTime, nextMonthStart);
                    }
                }
            }
        }

        // 添加其他查询条件
        lqw.eq(record.getMeterBookId() != null, WaterfeeMeterReadingRecord::getMeterBookId, record.getMeterBookId());
        lqw.eq(record.getTaskId() != null, WaterfeeMeterReadingRecord::getTaskId, record.getTaskId());
        lqw.like(record.getMeterNo() != null, WaterfeeMeterReadingRecord::getMeterNo, record.getMeterNo());
        lqw.eq(record.getMeterType() != null, WaterfeeMeterReadingRecord::getMeterType, record.getMeterType());
        lqw.eq(record.getIsAudited() != null, WaterfeeMeterReadingRecord::getIsAudited, record.getIsAudited());
        lqw.eq(record.getIsPending() != null, WaterfeeMeterReadingRecord::getIsPending, record.getIsPending());

        // 执行分页查询
        Page<MeterReadingRecordVo> page = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(page);
    }

    /**
     * 判断某个抄表任务是否已为指定水表生成记录
     */
    @Override
    public boolean existsByTaskIdAndMeterNo(Long taskId, String meterNo) {
        LambdaQueryWrapper<WaterfeeMeterReadingRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WaterfeeMeterReadingRecord::getTaskId, taskId);
        wrapper.eq(WaterfeeMeterReadingRecord::getMeterNo, meterNo);
        return baseMapper.selectCount(wrapper) > 0;
    }

    @Override
    public boolean existsByQuarterAndMeterNo(Date date, Long taskId, String meterNo) {
        // 获取当前季度对应的起止日期
        Date[] quarterRange = getQuarterRange(date);
        Date startDate = quarterRange[0];
        Date endDate = quarterRange[1];

        LambdaQueryWrapper<WaterfeeMeterReadingRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WaterfeeMeterReadingRecord::getMeterNo, meterNo);
        wrapper.between(WaterfeeMeterReadingRecord::getReadingTime, startDate, endDate);

        return baseMapper.selectCount(wrapper) > 0;
    }

    /**
     * 获取某个日期所属季度的起止时间
     *
     * @param inputDate
     * @return
     */
    private Date[] getQuarterRange(Date inputDate) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(inputDate);
        int year = cal.get(Calendar.YEAR);
        int month = cal.get(Calendar.MONTH) + 1; // Calendar.MONTH 从0开始

        Calendar startCal = Calendar.getInstance();
        Calendar endCal = Calendar.getInstance();

        if (month == 12 || month == 1 || month == 2) {
            // 第四季度：12.1 ~ 次年2月底
            if (month == 12) {
                startCal.set(year, Calendar.DECEMBER, 1, 0, 0, 0);
                endCal.set(year + 1, Calendar.FEBRUARY, endOfMonth(year + 1, 2), 23, 59, 59);
            } else {
                startCal.set(year - 1, Calendar.DECEMBER, 1, 0, 0, 0);
                endCal.set(year, Calendar.FEBRUARY, endOfMonth(year, 2), 23, 59, 59);
            }
        } else if (month >= 3 && month <= 5) {
            // 第一季度：3.1 ~ 5.31
            startCal.set(year, Calendar.MARCH, 1, 0, 0, 0);
            endCal.set(year, Calendar.MAY, 31, 23, 59, 59);
        } else if (month >= 6 && month <= 8) {
            // 第二季度：6.1 ~ 8.31
            startCal.set(year, Calendar.JUNE, 1, 0, 0, 0);
            endCal.set(year, Calendar.AUGUST, 31, 23, 59, 59);
        } else {
            // 第三季度：9.1 ~ 11.30
            startCal.set(year, Calendar.SEPTEMBER, 1, 0, 0, 0);
            endCal.set(year, Calendar.NOVEMBER, 30, 23, 59, 59);
        }

        return new Date[]{startCal.getTime(), endCal.getTime()};
    }

    // 获取某月的最大天数
    private int endOfMonth(int year, int month) {
        Calendar cal = Calendar.getInstance();
        cal.set(year, month - 1, 1); // 月份从0开始
        return cal.getActualMaximum(Calendar.DAY_OF_MONTH);
    }

    /**
     * 挂起抄表记录
     *
     * @param recordId 抄表记录ID
     * @param reason   挂起原因
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean pendingRecord(Long recordId, String reason) {
        WaterfeeMeterReadingRecord record = baseMapper.selectById(recordId);

        if (record == null) {
            log.error("尝试挂起不存在的抄表记录: {}", recordId);
            return false;
        }

        record.setWaterUsage(null); // 确保不设置水量，让数据库自动计算
        // 检查是否已经是挂起状态
        if (record.getIsPending() != null && "1".equals(record.getIsPending())) {
            log.info("抄表记录已经是挂起状态: {}", recordId);
            return true;
        }

        // 检查是否已审核
        if (record.getIsAudited() != null && "1".equals(record.getIsAudited())) {
            log.error("已审核的抄表记录不能挂起: {}", recordId);
            throw new RuntimeException("已审核的抄表记录不能挂起");
        }

        // 设置挂起状态和原因
        record.setIsPending("1");
        record.setPendingReason(reason);

        log.info("挂起抄表记录: {}, 原因: {}", recordId, reason);
        return baseMapper.updateById(record) > 0;
    }

    /**
     * 取消挂起抄表记录
     *
     * @param recordId 抄表记录ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelPendingRecord(Long recordId) {
        WaterfeeMeterReadingRecord record = baseMapper.selectById(recordId);

        if (record == null) {
            log.error("尝试取消挂起不存在的抄表记录: {}", recordId);
            return false;
        }

        record.setWaterUsage(null); // 确保不设置水量，让数据库自动计算
        // 检查是否处于挂起状态
        if (record.getIsPending() == null || !"1".equals(record.getIsPending())) {
            log.info("抄表记录不是挂起状态，无需取消挂起: {}", recordId);
            return true;
        }

        // 取消挂起状态和原因
        record.setIsPending("0");
        record.setPendingReason(null);

        log.info("取消挂起抄表记录: {}", recordId);
        return baseMapper.updateById(record) > 0;
    }

    // 内部类定义
    @Getter
    private static class BillCalculationResult {
        private final BigDecimal totalCharge;
        private final Long priceConfigId;
        private final Map<Integer, BigDecimal> tierUsageMap;
        private final Map<Integer, BigDecimal> tierAmountMap;

        public BillCalculationResult(BigDecimal totalCharge, Long priceConfigId,
                                     Map<Integer, BigDecimal> tierUsageMap,
                                     Map<Integer, BigDecimal> tierAmountMap) {
            this.totalCharge = totalCharge;
            this.priceConfigId = priceConfigId;
            this.tierUsageMap = tierUsageMap;
            this.tierAmountMap = tierAmountMap;
        }

    }

    @Getter
    private static class TierCalculationResult {
        private final BigDecimal tierUsage;
        private final BigDecimal tierAmount;

        public TierCalculationResult(BigDecimal tierUsage, BigDecimal tierAmount) {
            this.tierUsage = tierUsage;
            this.tierAmount = tierAmount;
        }

    }

    @Getter
    private static class TierRange {
        private final BigDecimal start;
        private final BigDecimal end;

        public TierRange(BigDecimal start, BigDecimal end) {
            this.start = start;
            this.end = end;
        }

    }

    @Getter
    private static class BillFeeBreakdown {
        private final BigDecimal waterResourceTax;
        private final BigDecimal sewageTreatmentFee;
        private final BigDecimal waterBillOnly;

        public BillFeeBreakdown(BigDecimal waterResourceTax, BigDecimal sewageTreatmentFee, BigDecimal waterBillOnly) {
            this.waterResourceTax = waterResourceTax;
            this.sewageTreatmentFee = sewageTreatmentFee;
            this.waterBillOnly = waterBillOnly;
        }

    }
}
