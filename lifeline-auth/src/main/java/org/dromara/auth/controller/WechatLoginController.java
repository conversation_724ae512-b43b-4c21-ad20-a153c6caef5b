package org.dromara.auth.controller;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.auth.service.WechatLoginService;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.utils.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.io.PrintWriter;

@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/wechat")
public class WechatLoginController {

    @Value("${wechat.appId}")
    private String appId;

    @Value("${wechat.appSecret}")
    private String appSecret;

    private final WechatLoginService wechatLoginService;

    /**
     * 微信公众号配置URL回调
     * @param request
     * @param response
     */
    @GetMapping("/valid")
    @ResponseBody
    public void wechatURLValid(HttpServletRequest request, HttpServletResponse response) {
        // 微信加密签名，signature结合了开发者填写的token参数和请求中的timestamp参数、nonce参数。
        String signature = request.getParameter("signature");
        // 时间戳
        String timestamp = request.getParameter("timestamp");
        // 随机数
        String nonce = request.getParameter("nonce");
        // 随机字符串
        String echostr = request.getParameter("echostr");


        PrintWriter out = null;
        try {
            out = response.getWriter();
            // 通过检验signature对请求进行校验，若校验成功则原样返回echostr，表示接入成功，否则接入失败
            if (wechatLoginService.checkSignature(signature, timestamp, nonce)) {
                out.print(echostr);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (out != null) {
                out.close();
            }
        }
    }

    @GetMapping("/openid")
    public R getOpenid(@RequestParam String code) {
        // 1. 校验code
        if (StringUtils.isEmpty(code)) {
            return R.fail("Code is required");
        }

        // 2. 请求微信API
        String url = "https://api.weixin.qq.com/sns/oauth2/access_token?" +
            "appid=" + appId +
            "&secret=" + appSecret +
            "&code=" + code +
            "&grant_type=authorization_code";

        // 3. 使用RestTemplate发送请求
        RestTemplate restTemplate = new RestTemplate();
        String response = restTemplate.getForObject(url, String.class);

        // 4. 解析响应
        JSONObject json = JSON.parseObject(response);
        if (json.containsKey("openid")) {
            JSONObject openidJson = new JSONObject();
            openidJson.put("openid", json.getString("openid"));
            return R.ok(openidJson);
        } else {
            return R.fail("Wechat error: " + json.getString("errmsg"));
        }
    }

}
